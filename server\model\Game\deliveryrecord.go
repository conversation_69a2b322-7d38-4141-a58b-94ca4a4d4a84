// 自动生成模板Deliveryrecord
package Game

import (
	"time"
)

// 提取记录 结构体  Deliveryrecord
type Deliveryrecord struct {
	Id       *int       `json:"id" form:"id" gorm:"primarykey;column:id;"size:19;`                  //id字段
	GameId   *int       `json:"gameId" form:"gameId" gorm:"comment:所属游戏ID;column:game_id;"size:19;` //所属游戏ID
	Code     *string    `json:"code" form:"code" gorm:"comment:角色代码;column:code;"size:255;`         //角色代码
	Price    *int       `json:"price" form:"price" gorm:"comment:价格;column:price;"size:10;`         //价格
	User     *string    `json:"user" form:"user" gorm:"comment:用户;column:user;"size:255;`           //用户
	PickDate *time.Time `json:"pickDate" form:"pickDate" gorm:"comment:提取时间;column:pickDate;"`      //提取时间
	Info     *string    `json:"info" form:"info"gorm:"type:varchar(991);;column:info;"size:999;`    //账号信息
}

// TableName 提取记录 Deliveryrecord自定义表名 deliveryrecord
func (Deliveryrecord) TableName() string {
	return "deliveryrecord"
}
