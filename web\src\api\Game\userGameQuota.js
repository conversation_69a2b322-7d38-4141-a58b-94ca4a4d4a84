import service from '@/utils/request'
// @Tags UserGameQuota
// @Summary 创建userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.UserGameQuota true "创建userGameQuota表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /userGameQuota/createUserGameQuota [post]
export const createUserGameQuota = (data) => {
  return service({
    url: '/userGameQuota/createUserGameQuota',
    method: 'post',
    data
  })
}

// @Tags UserGameQuota
// @Summary 删除userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.UserGameQuota true "删除userGameQuota表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userGameQuota/deleteUserGameQuota [delete]
export const deleteUserGameQuota = (params) => {
  return service({
    url: '/userGameQuota/deleteUserGameQuota',
    method: 'delete',
    params
  })
}

// @Tags UserGameQuota
// @Summary 批量删除userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除userGameQuota表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userGameQuota/deleteUserGameQuota [delete]
export const deleteUserGameQuotaByIds = (params) => {
  return service({
    url: '/userGameQuota/deleteUserGameQuotaByIds',
    method: 'delete',
    params
  })
}

// @Tags UserGameQuota
// @Summary 更新userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.UserGameQuota true "更新userGameQuota表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userGameQuota/updateUserGameQuota [put]
export const updateUserGameQuota = (data) => {
  return service({
    url: '/userGameQuota/updateUserGameQuota',
    method: 'put',
    data
  })
}

// @Tags UserGameQuota
// @Summary 用id查询userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.UserGameQuota true "用id查询userGameQuota表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userGameQuota/findUserGameQuota [get]
export const findUserGameQuota = (params) => {
  return service({
    url: '/userGameQuota/findUserGameQuota',
    method: 'get',
    params
  })
}

// @Tags UserGameQuota
// @Summary 分页获取userGameQuota表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取userGameQuota表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userGameQuota/getUserGameQuotaList [get]
export const getUserGameQuotaList = (params) => {
  return service({
    url: '/userGameQuota/getUserGameQuotaList',
    method: 'get',
    params
  })
}

// @Tags UserGameQuota
// @Summary 不需要鉴权的userGameQuota表接口
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.UserGameQuotaSearch true "分页获取userGameQuota表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /userGameQuota/getUserGameQuotaPublic [get]
export const getUserGameQuotaPublic = () => {
  return service({
    url: '/userGameQuota/getUserGameQuotaPublic',
    method: 'get',
  })
}



// SyncUserGameQuota syncUserGameQuota
// @Tags UserGameQuota
// @Summary syncUserGameQuota
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "成功"
// @Router /userGameQuota/syncUserGameQuota [GET]
export const syncUserGameQuota = () => {
  return service({
    url: '/userGameQuota/syncUserGameQuota',
    method: 'GET'
  })
}
// GetUserEfficiency 获取用户额度
// @Tags UserGameQuota
// @Summary 获取用户额度
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "成功"
// @Router /userGameQuota/GetUserEfficiency [GET]
export const GetUserEfficiency = () => {
  return service({
    url: '/userGameQuota/GetUserEfficiency',
    method: 'GET'
  })
}
