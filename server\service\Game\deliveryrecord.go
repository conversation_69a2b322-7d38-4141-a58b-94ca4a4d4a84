package Game

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
)

type DeliveryrecordService struct{}

// CreateDeliveryrecord 创建提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) CreateDeliveryrecord(ctx context.Context, deliveryrecord *Game.Deliveryrecord) (err error) {
	err = global.GVA_DB.Create(deliveryrecord).Error
	return err
}

// DeleteDeliveryrecord 删除提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) DeleteDeliveryrecord(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&Game.Deliveryrecord{}, "id = ?", id).Error
	return err
}

// DeleteDeliveryrecordByIds 批量删除提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) DeleteDeliveryrecordByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]Game.Deliveryrecord{}, "id in ?", ids).Error
	return err
}

// UpdateDeliveryrecord 更新提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) UpdateDeliveryrecord(ctx context.Context, deliveryrecord Game.Deliveryrecord) (err error) {
	err = global.GVA_DB.Model(&Game.Deliveryrecord{}).Where("id = ?", deliveryrecord.Id).Updates(&deliveryrecord).Error
	return err
}

// GetDeliveryrecord 根据id获取提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) GetDeliveryrecord(ctx context.Context, id string) (deliveryrecord Game.Deliveryrecord, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&deliveryrecord).Error
	return
}

// GetDeliveryrecordInfoList 分页获取提取记录记录
// Author [yourname](https://github.com/yourname)
func (deliveryrecordService *DeliveryrecordService) GetDeliveryrecordInfoList(ctx context.Context, info GameReq.DeliveryrecordSearch) (list []Game.Deliveryrecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&Game.Deliveryrecord{})
	var deliveryrecords []Game.Deliveryrecord
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.GameId != nil {
		db = db.Where("game_id = ?", *info.GameId)
	}
	if info.Code != nil && *info.Code != "" {
		db = db.Where("code LIKE ?", "%"+*info.Code+"%")
	}
	if info.Price != nil {
		db = db.Where("price = ?", *info.Price)
	}
	if info.User != nil && *info.User != "" {
		db = db.Where("user = ?", *info.User)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 添加排序逻辑
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["id"] = true        // 支持按ID排序
	orderMap["pick_date"] = true // 支持按提取时间排序
	orderMap["price"] = true     // 支持按价格排序
	orderMap["game_id"] = true   // 支持按游戏ID排序

	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc" // 降序排列
		}
		db = db.Order(OrderStr)
	} else {
		// 默认按ID降序排列（最新记录在前）
		db = db.Order("id desc")
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&deliveryrecords).Error
	return deliveryrecords, total, err
}
func (deliveryrecordService *DeliveryrecordService) GetDeliveryrecordPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
