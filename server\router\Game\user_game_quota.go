package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserGameQuotaRouter struct{}

func (s *UserGameQuotaRouter) InitUserGameQuotaRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	userGameQuotaRouter := Router.Group("userGameQuota").Use(middleware.OperationRecord())
	userGameQuotaRouterWithoutRecord := Router.Group("userGameQuota")
	userGameQuotaRouterWithoutAuth := PublicRouter.Group("userGameQuota")
	{
		userGameQuotaRouter.POST("createUserGameQuota", userGameQuotaApi.CreateUserGameQuota)
		userGameQuotaRouter.DELETE("deleteUserGameQuota", userGameQuotaApi.DeleteUserGameQuota)
		userGameQuotaRouter.DELETE("deleteUserGameQuotaByIds", userGameQuotaApi.DeleteUserGameQuotaByIds)
		userGameQuotaRouter.PUT("updateUserGameQuota", userGameQuotaApi.UpdateUserGameQuota)
		userGameQuotaRouter.GET("syncUserGameQuota", userGameQuotaApi.SyncUserGameQuota)
		userGameQuotaRouter.GET("GetUserEfficiency", userGameQuotaApi.GetUserEfficiency)
	}
	{
		userGameQuotaRouterWithoutRecord.GET("findUserGameQuota", userGameQuotaApi.FindUserGameQuota)
		userGameQuotaRouterWithoutRecord.GET("getUserGameQuotaList", userGameQuotaApi.GetUserGameQuotaList)
	}
	{
		userGameQuotaRouterWithoutAuth.GET("getUserGameQuotaPublic", userGameQuotaApi.GetUserGameQuotaPublic)
	}
}
