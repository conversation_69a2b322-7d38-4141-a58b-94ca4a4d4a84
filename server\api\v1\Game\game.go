package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type GameApi struct{}

// CreateGame 创建game表
// @Tags Game
// @Summary 创建game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Game true "创建game表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /game/createGame [post]
func (gameApi *GameApi) CreateGame(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var game Game.Game
	err := c.ShouldBindJSON(&game)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = gameService.CreateGame(ctx, &game)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteGame 删除game表
// @Tags Game
// @Summary 删除game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Game true "删除game表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /game/deleteGame [delete]
func (gameApi *GameApi) DeleteGame(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := gameService.DeleteGame(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteGameByIds 批量删除game表
// @Tags Game
// @Summary 批量删除game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /game/deleteGameByIds [delete]
func (gameApi *GameApi) DeleteGameByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := gameService.DeleteGameByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateGame 更新game表
// @Tags Game
// @Summary 更新game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Game true "更新game表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /game/updateGame [put]
func (gameApi *GameApi) UpdateGame(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var game Game.Game
	err := c.ShouldBindJSON(&game)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = gameService.UpdateGame(ctx, game)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindGame 用id查询game表
// @Tags Game
// @Summary 用id查询game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询game表"
// @Success 200 {object} response.Response{data=Game.Game,msg=string} "查询成功"
// @Router /game/findGame [get]
func (gameApi *GameApi) FindGame(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	regame, err := gameService.GetGame(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(regame, c)
}

// GetGameList 分页获取game表列表
// @Tags Game
// @Summary 分页获取game表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.GameSearch true "分页获取game表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /game/getGameList [get]
func (gameApi *GameApi) GetGameList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo GameReq.GameSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := gameService.GetGameInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// SyncGame 同步游戏列表
// @Tags Game
func (gameApi *GameApi) SyncGame(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()
	err := gameService.SyncGame(ctx)
	if err != nil {
		global.GVA_LOG.Error("同步失败!", zap.Error(err))
		response.FailWithMessage("同步失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("同步成功", c)
}

// GetGamePublic 不需要鉴权的game表接口
// @Tags Game
// @Summary 不需要鉴权的game表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /game/getGamePublic [get]
func (gameApi *GameApi) GetGamePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	gameService.GetGamePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的game表接口信息",
	}, "获取成功", c)
}
