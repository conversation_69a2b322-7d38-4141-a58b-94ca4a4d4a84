#!/usr/bin/env python
import os
import subprocess
import shutil
from pathlib import Path

# 数据库配置和黑名单
DB_HOST = "localhost"
DB_USER = "root"
DB_PASSWORD = "password"
DB_NAME = "your_database"
TABLE_BLACKLIST = ["table1", "table2"]


def check_tool(tool_name):
    try:
        process = subprocess.run(["where", tool_name], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return True
    except subprocess.CalledProcessError:
        return False


def build_frontend():
    print("开始构建前端...")
    try:
        process = subprocess.run(["npm", "run", "build"], cwd="web", check=True)
   
        print("前端构建成功！")
    except subprocess.CalledProcessError as e:
        print(f"错误: npm run build 执行失败，错误码：{e.returncode}")
        raise


def build_backend():
    print("开始构建后端...")
    try:
        env = os.environ.copy()
        env["GO111MODULE"] = "on"
        env["GOOS"] = "linux"
        process = subprocess.run(["go", "build", "-ldflags", "-w -s"], cwd="server", env=env, check=True)
  
        print("后端构建成功！")
    except subprocess.CalledProcessError as e:
        print(f"错误: go build 执行失败，错误码：{e.returncode}")
        raise


def package_files(use_zip):
    print("开始打包...")
    if use_zip:
        print("使用zip进行打包...")
        zip_path = Path("server.zip")
        if zip_path.exists():
            zip_path.unlink()
        files_to_zip = [
            "server/server.exe",
            "server/config.yaml",
            "web/dist"
        ]
        try:
            shutil.make_archive("server", "zip", root_dir=".", base_dir=".", 
                              verbose=1, dry_run=False, owner=None, group=None,
                              logger=None, base_dirs=files_to_zip)
            print("打包完成: server.zip")
        except Exception as e:
            print(f"错误: zip打包失败，错误信息：{str(e)}")
            raise
    else:
        print("使用rar进行打包...")
        rar_path = Path("server.rar")
        if rar_path.exists():
            rar_path.unlink()
        try:
            process = subprocess.run(["rar", "a", "-ep1", "server.rar", "server/server", "config.yaml", "web/dist"], check=True)
          
            print("打包完成: server.rar")
        except subprocess.CalledProcessError as e:
            print(f"错误: rar打包失败，错误码：{e.returncode}")
            raise


def export_database():
    print("开始导出数据库...")
    try:
        # 导出黑名单表的结构
        blacklist_tables = " ".join(TABLE_BLACKLIST)
        blacklist_cmd = f"mysqldump -h {DB_HOST} -u {DB_USER} -p{DB_PASSWORD} --no-data {DB_NAME} {blacklist_tables}"
        blacklist_output = subprocess.check_output(blacklist_cmd, shell=True, stderr=subprocess.STDOUT)

        # 导出非黑名单表的结构和数据
        non_blacklist_tables = [table for table in get_all_tables() if table not in TABLE_BLACKLIST]
        non_blacklist_tables_str = " ".join(non_blacklist_tables)
        non_blacklist_cmd = f"mysqldump -h {DB_HOST} -u {DB_USER} -p{DB_PASSWORD} {DB_NAME} {non_blacklist_tables_str}"
        non_blacklist_output = subprocess.check_output(non_blacklist_cmd, shell=True, stderr=subprocess.STDOUT)

        # 合并导出结果
        with open("database_export.sql", "wb") as f:
            f.write(blacklist_output)
            f.write(non_blacklist_output)

        print("数据库导出成功！")
    except subprocess.CalledProcessError as e:
        print(f"错误: 数据库导出失败，错误信息：{e.output.decode('utf-8')}")
        raise


def get_all_tables():
    try:
        cmd = f"mysql -h {DB_HOST} -u {DB_USER} -p{DB_PASSWORD} -D {DB_NAME} -N -e 'SHOW TABLES'"
        output = subprocess.check_output(cmd, shell=True, stderr=subprocess.STDOUT)
        return output.decode('utf-8').strip().split("\n")
    except subprocess.CalledProcessError as e:
        print(f"错误: 获取表列表失败，错误信息：{e.output.decode('utf-8')}")
        raise


def main():
    print("开始构建项目...")
    
    # 检查工具
    if not check_tool("go"):
        print("错误: 未找到go命令，请确保Go已安装并添加到PATH环境变量中")
        return 1
    
    if not check_tool("npm"):
        print("错误: 未找到npm命令，请确保Node.js已安装并添加到PATH环境变量中")
        return 1
    
    # if not check_tool("mysqldump"):
    #     print("错误: 未找到mysqldump命令，请确保MySQL客户端已安装并添加到PATH环境变量中")
    #     return 1
    
    use_zip = not check_tool("rar")
    if use_zip:
        print("警告: 未找到rar命令，将使用zip作为备选")
    
    try:
        build_frontend()
        build_backend()
        package_files(use_zip)
        export_database()
        print("构建、打包和数据库导出过程全部完成！")
        return 0
    except Exception:
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())