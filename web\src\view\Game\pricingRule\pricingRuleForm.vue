
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="指定角色代码列表，逗号分隔:" prop="roleCodes">
    <el-input v-model="formData.roleCodes" :clearable="true" placeholder="请输入指定角色代码列表，逗号分隔" />
</el-form-item>
        <el-form-item label="指定角色基础价:" prop="roleBasePrice">
    <el-input-number v-model="formData.roleBasePrice" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="指定服务器代码:" prop="serverCode">
    <el-input v-model="formData.serverCode" :clearable="true" placeholder="请输入指定服务器代码" />
</el-form-item>
        <el-form-item label="服务器溢价百分比:" prop="serverPremium">
    <el-input-number v-model="formData.serverPremium" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="未指定角色定价倍率:" prop="unspecifiedMultiplier">
    <el-input-number v-model="formData.unspecifiedMultiplier" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="数量条件:" prop="quantityCondition">
    <el-input v-model="formData.quantityCondition" :clearable="true" placeholder="请输入数量条件" />
</el-form-item>
        <el-form-item label="整体折扣率(10%-200%):" prop="discountRate">
    <el-input-number v-model="formData.discountRate" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createPricingRule,
  updatePricingRule,
  findPricingRule
} from '@/api/Game/pricingRule'

defineOptions({
    name: 'PricingRuleForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            roleCodes: '',
            roleBasePrice: 0,
            serverCode: '',
            serverPremium: 0,
            unspecifiedMultiplier: 0,
            quantityCondition: '',
            discountRate: 0,
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findPricingRule({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createPricingRule(formData.value)
               break
             case 'update':
               res = await updatePricingRule(formData.value)
               break
             default:
               res = await createPricingRule(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
