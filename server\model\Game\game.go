
// 自动生成模板Game
package Game
import (
	"time"
)

// game表 结构体  Game
type Game struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;comment:游戏ID;column:id;"size:10;`  //游戏ID
  Name  *string `json:"name" form:"name" gorm:"comment:游戏名称;column:name;"size:50;`  //游戏名称
  Code  *string `json:"code" form:"code" gorm:"comment:游戏代码;column:code;"size:20;`  //游戏代码
  IsEnable  *int `json:"isEnable" form:"isEnable" gorm:"comment:是否启用;column:isEnable;"size:10;`  //是否启用
  Description  *string `json:"description" form:"description" gorm:"comment:游戏描述;column:description;"`  //游戏描述
  CreatedAt  *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`  //创建时间
  UpdatedAt  *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`  //更新时间
}


// TableName game表 Game自定义表名 game
func (Game) TableName() string {
    return "game"
}





