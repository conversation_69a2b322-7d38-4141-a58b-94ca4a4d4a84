import service from '@/utils/request'
// @Tags Deliveryrecord
// @Summary 创建提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Deliveryrecord true "创建提取记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /deliveryrecord/createDeliveryrecord [post]
export const createDeliveryrecord = (data) => {
  return service({
    url: '/deliveryrecord/createDeliveryrecord',
    method: 'post',
    data
  })
}

// @Tags Deliveryrecord
// @Summary 删除提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Deliveryrecord true "删除提取记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /deliveryrecord/deleteDeliveryrecord [delete]
export const deleteDeliveryrecord = (params) => {
  return service({
    url: '/deliveryrecord/deleteDeliveryrecord',
    method: 'delete',
    params
  })
}

// @Tags Deliveryrecord
// @Summary 批量删除提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除提取记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /deliveryrecord/deleteDeliveryrecord [delete]
export const deleteDeliveryrecordByIds = (params) => {
  return service({
    url: '/deliveryrecord/deleteDeliveryrecordByIds',
    method: 'delete',
    params
  })
}

// @Tags Deliveryrecord
// @Summary 更新提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Deliveryrecord true "更新提取记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /deliveryrecord/updateDeliveryrecord [put]
export const updateDeliveryrecord = (data) => {
  return service({
    url: '/deliveryrecord/updateDeliveryrecord',
    method: 'put',
    data
  })
}

// @Tags Deliveryrecord
// @Summary 用id查询提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Deliveryrecord true "用id查询提取记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /deliveryrecord/findDeliveryrecord [get]
export const findDeliveryrecord = (params) => {
  return service({
    url: '/deliveryrecord/findDeliveryrecord',
    method: 'get',
    params
  })
}

// @Tags Deliveryrecord
// @Summary 分页获取提取记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取提取记录列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /deliveryrecord/getDeliveryrecordList [get]
export const getDeliveryrecordList = (params) => {
  return service({
    url: '/deliveryrecord/getDeliveryrecordList',
    method: 'get',
    params
  })
}

// @Tags Deliveryrecord
// @Summary 不需要鉴权的提取记录接口
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.DeliveryrecordSearch true "分页获取提取记录列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deliveryrecord/getDeliveryrecordPublic [get]
export const getDeliveryrecordPublic = () => {
  return service({
    url: '/deliveryrecord/getDeliveryrecordPublic',
    method: 'get',
  })
}
