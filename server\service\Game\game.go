package Game

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
)

type GameService struct{}

// CreateGame 创建game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) CreateGame(ctx context.Context, game *Game.Game) (err error) {
	err = global.GVA_DB.Create(game).Error
	return err
}

// DeleteGame 删除game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) DeleteGame(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&Game.Game{}, "id = ?", id).Error
	return err
}

// DeleteGameByIds 批量删除game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) DeleteGameByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]Game.Game{}, "id in ?", ids).Error
	return err
}

// UpdateGame 更新game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) UpdateGame(ctx context.Context, game Game.Game) (err error) {
	err = global.GVA_DB.Model(&Game.Game{}).Where("id = ?", game.Id).Updates(&game).Error
	return err
}

// GetGame 根据id获取game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) GetGame(ctx context.Context, id string) (game Game.Game, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&game).Error
	return
}

// GetGameInfoList 分页获取game表记录
// Author [yourname](https://github.com/yourname)
func (gameService *GameService) GetGameInfoList(ctx context.Context, info GameReq.GameSearch) (list []Game.Game, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&Game.Game{})
	var games []Game.Game
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.Code != nil && *info.Code != "" {
		db = db.Where("code = ?", *info.Code)
	}
	if info.IsEnable != nil {
		db = db.Where("isEnable = ?", *info.IsEnable)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&games).Error
	return games, total, err
}
func (gameService *GameService) GetGamePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// SyncGame 同步游戏列表
func (gameService *GameService) SyncGame(ctx context.Context) (err error) {
	// 从data_news_item表中获取游戏列表,同步到game表
	// 1. 查询data_news_item表中的游戏数据
	type NewsItem struct {
		Id   int    `json:"id"`
		Name string `json:"name"`
		Code string `json:"code"`
	}
	var newsItems []NewsItem
	err = global.GVA_DB.Table("data_news_item").Select([]string{"id", "name", "code"}).Find(&newsItems).Error
	if err != nil {
		return err
	}

	// 2. 遍历查询结果，更新或创建game表记录
	for _, item := range newsItems {
		var count int64
		// 检查该游戏是否已存在
		err = global.GVA_DB.Model(&Game.Game{}).Where("id = ?", item.Id).Count(&count).Error
		if err != nil {
			return err
		}

		// 默认启用状态
		isEnable := 1
		// 构建游戏对象
		game := Game.Game{
			Id:       &item.Id,
			Name:     &item.Name,
			Code:     &item.Code,
			IsEnable: &isEnable,
		}

		if count > 0 {
			// 更新已存在的记录
			err = global.GVA_DB.Model(&Game.Game{}).Where("id = ?", item.Id).Updates(&game).Error
		} else {
			// 创建新记录
			err = global.GVA_DB.Create(&game).Error
		}

		if err != nil {
			return err
		}
	}

	return nil
}
