package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type PricingRuleRouter struct{}

// InitPricingRuleRouter 初始化 pricingRule表 路由信息
func (s *PricingRuleRouter) InitPricingRuleRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	pricingRuleRouter := Router.Group("pricingRule").Use(middleware.OperationRecord())
	pricingRuleRouterWithoutRecord := Router.Group("pricingRule")
	pricingRuleRouterWithoutAuth := PublicRouter.Group("pricingRule")
	{
		pricingRuleRouter.POST("createPricingRule", pricingRuleApi.CreatePricingRule)             // 新建pricingRule表
		pricingRuleRouter.DELETE("deletePricingRule", pricingRuleApi.DeletePricingRule)           // 删除pricingRule表
		pricingRuleRouter.DELETE("deletePricingRuleByIds", pricingRuleApi.DeletePricingRuleByIds) // 批量删除pricingRule表
		pricingRuleRouter.PUT("updatePricingRule", pricingRuleApi.UpdatePricingRule)              // 更新pricingRule表
	}
	{
		pricingRuleRouterWithoutRecord.GET("findPricingRule", pricingRuleApi.FindPricingRule)       // 根据ID获取pricingRule表
		pricingRuleRouterWithoutRecord.GET("getPricingRuleList", pricingRuleApi.GetPricingRuleList) // 获取pricingRule表列表
		pricingRuleRouterWithoutRecord.GET("calePricing", pricingRuleApi.CalePricing)               // 获取pricingRule表列表
	}
	{
		pricingRuleRouterWithoutAuth.GET("getPricingRulePublic", pricingRuleApi.GetPricingRulePublic) // pricingRule表开放接口
	}
}
