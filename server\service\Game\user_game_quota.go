package Game

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

type UserGameQuotaService struct{}

// CreateUserGameQuota 创建userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) CreateUserGameQuota(ctx context.Context, userGameQuota *Game.UserGameQuota) (err error) {
	err = global.GVA_DB.Create(userGameQuota).Error
	return err
}

// DeleteUserGameQuota 删除userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) DeleteUserGameQuota(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&Game.UserGameQuota{}, "id = ?", id).Error
	return err
}

// DeleteUserGameQuotaByIds 批量删除userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) DeleteUserGameQuotaByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]Game.UserGameQuota{}, "id in ?", ids).Error
	return err
}

// UpdateUserGameQuota 更新userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) UpdateUserGameQuota(ctx context.Context, userGameQuota Game.UserGameQuota) (err error) {
	err = global.GVA_DB.Model(&Game.UserGameQuota{}).Where("id = ?", userGameQuota.Id).Updates(&userGameQuota).Error
	return err
}

// GetUserGameQuota 根据id获取userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) GetUserGameQuota(ctx context.Context, id string) (userGameQuota Game.UserGameQuota, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&userGameQuota).Error
	return
}

// GetUserGameQuotaInfoList 分页获取userGameQuota表记录
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) GetUserGameQuotaInfoList(ctx context.Context, info GameReq.UserGameQuotaSearch) (list []Game.UserGameQuota, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&Game.UserGameQuota{})
	var userGameQuotas []Game.UserGameQuota
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Username != nil && *info.Username != "" {
		db = db.Where("username = ?", *info.Username)
	}
	if info.GameId != nil {
		db = db.Where("game_id = ?", *info.GameId)
	}
	if info.TotalQuota != nil {
		db = db.Where("total_quota = ?", *info.TotalQuota)
	}
	if info.UsedQuota != nil {
		db = db.Where("used_quota = ?", *info.UsedQuota)
	}
	if info.DailyLimit != nil {
		db = db.Where("daily_limit = ?", *info.DailyLimit)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["total_quota"] = true
	orderMap["used_quota"] = true
	orderMap["daily_limit"] = true
	orderMap["today_used"] = true
	orderMap["status"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&userGameQuotas).Error
	return userGameQuotas, total, err
}
func (userGameQuotaService *UserGameQuotaService) GetUserGameQuotaPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// SyncUserGameQuota syncUserGameQuota
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) SyncUserGameQuota(ctx context.Context) (err error) {
	// 请在这里实现自己的业务逻辑
	//从sys_users 获取所有用户的 username
	var users []system.SysUser
	err = global.GVA_DB.Model(&system.SysUser{}).Find(&users).Error
	if err != nil {
		return err
	}

	for _, user := range users {
		// 检查用户是否已存在于user_game_quota表中，避免重复插入
		var count int64
		err = global.GVA_DB.Model(&Game.UserGameQuota{}).Where("username = ?", user.Username).Count(&count).Error
		if err != nil {
			continue // 查询出错时跳过当前用户
		}

		// 如果用户已存在，跳过插入操作
		if count > 0 {
			continue // 用户已存在，不重复插入
		}

		//写入到user_game_quota表中（仅当用户不存在时）
		//var id = int(user.GVA_MODEL.ID)
		var userGameQuota Game.UserGameQuota
		//userGameQuota.Id = &id
		db := global.GVA_DB.Model(&Game.UserGameQuota{})

		userGameQuota.Username = &user.Username
		err = db.Create(&userGameQuota).Error
		if err != nil {
			continue // 插入失败时跳过当前用户
		}
	}
	return err
}

// GetUserEfficiency 获取用户额度
// Author [yourname](https://github.com/yourname)
func (userGameQuotaService *UserGameQuotaService) GetUserEfficiency(ctx context.Context, username string) (map[string]interface{}, error) {
	// 请在这里实现自己的业务逻辑
	// db := global.GVA_DB.Model(&Game.UserGameQuota{})
	// return db.Error
	// 结果map
	result := make(map[string]interface{})

	// 获取用户游戏额度信息
	var userQuota Game.UserGameQuota
	err := global.GVA_DB.Where("username = ? ", username).First(&userQuota).Error
	if err != nil {
		return nil, errors.New("获取用户额度信息失败: " + err.Error())
	}

	// 计算额度使用率
	var quotaUsageRate float64 = 0
	if userQuota.TotalQuota != nil && *userQuota.TotalQuota > 0 {
		if userQuota.UsedQuota == nil {
			userQuota.UsedQuota = new(int)
			*userQuota.UsedQuota = 0
		}
		quotaUsageRate = float64(*userQuota.UsedQuota) / float64(*userQuota.TotalQuota) * 100
	}

	// 获取今日日期
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 获取本月第一天
	firstDayOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 获取今日提取记录数量
	var todayCount int64
	err = global.GVA_DB.Model(&Game.Deliveryrecord{}).
		Where("user = ? AND pickDate >= ?", username, today).
		Count(&todayCount).Error
	if err != nil {
		return nil, errors.New("获取今日提取记录失败: " + err.Error())
	}

	// 获取本月提取记录数量
	var monthCount int64
	err = global.GVA_DB.Model(&Game.Deliveryrecord{}).
		Where("user = ? AND pickDate >= ?", username, firstDayOfMonth).
		Count(&monthCount).Error
	if err != nil {
		return nil, errors.New("获取本月提取记录失败: " + err.Error())
	}

	// 获取今日提取总金额
	var todayAmount struct {
		Total int `json:"total"`
	}
	err = global.GVA_DB.Model(&Game.Deliveryrecord{}).
		Select("COALESCE(SUM(price), 0) as total").
		Where("user = ? AND pickDate >= ?", username, today).
		Scan(&todayAmount).Error
	if err != nil {
		return nil, errors.New("获取今日提取金额失败: " + err.Error())
	}

	// 获取本月提取总金额
	var monthAmount struct {
		Total int `json:"total"`
	}
	err = global.GVA_DB.Model(&Game.Deliveryrecord{}).
		Select("COALESCE(SUM(price), 0) as total").
		Where("user = ? AND pickDate >= ?", username, firstDayOfMonth).
		Scan(&monthAmount).Error
	if err != nil {
		return nil, errors.New("获取本月提取金额失败: " + err.Error())
	}

	// 计算今日效率（今日提取数量/今日已过小时数）
	hoursPassedToday := float64(now.Hour()) + float64(now.Minute())/60.0
	if hoursPassedToday < 1 {
		hoursPassedToday = 1 // 避免除以0
	}
	todayEfficiency := float64(todayCount) / hoursPassedToday

	// 计算本月效率（本月提取数量/本月已过天数）
	daysPassedThisMonth := float64(now.Day())
	if daysPassedThisMonth < 1 {
		daysPassedThisMonth = 1 // 避免除以0
	}
	monthEfficiency := float64(monthCount) / daysPassedThisMonth

	// 组装结果
	result["todayCount"] = todayCount                                 // 今日提取数量
	result["todayAmount"] = todayAmount.Total                         // 今日提取金额
	result["todayEfficiency"] = math.Round(todayEfficiency*100) / 100 // 今日效率（四舍五入到2位小数）
	result["monthCount"] = monthCount                                 // 本月提取数量
	result["monthAmount"] = monthAmount.Total                         // 本月提取金额
	result["monthEfficiency"] = math.Round(monthEfficiency*100) / 100 // 本月效率（四舍五入到2位小数）
	result["usedQuota"] = userQuota.UsedQuota                         // 已使用额度
	result["totalQuota"] = userQuota.TotalQuota                       // 总额度
	result["quotaUsageRate"] = math.Round(quotaUsageRate*100) / 100   // 额度使用率（四舍五入到2位小数）

	return result, nil
}
