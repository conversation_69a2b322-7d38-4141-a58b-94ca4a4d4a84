import service from '@/utils/request'
// @Tags Game
// @Summary 创建game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Game true "创建game表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /game/createGame [post]
export const createGame = (data) => {
  return service({
    url: '/game/createGame',
    method: 'post',
    data
  })
}

// @Tags Game
// @Summary 删除game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Game true "删除game表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /game/deleteGame [delete]
export const deleteGame = (params) => {
  return service({
    url: '/game/deleteGame',
    method: 'delete',
    params
  })
}

// @Tags Game
// @Summary 批量删除game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除game表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /game/deleteGame [delete]
export const deleteGameByIds = (params) => {
  return service({
    url: '/game/deleteGameByIds',
    method: 'delete',
    params
  })
}

// @Tags Game
// @Summary 更新game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Game true "更新game表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /game/updateGame [put]
export const updateGame = (data) => {
  return service({
    url: '/game/updateGame',
    method: 'put',
    data
  })
}

// @Tags Game
// @Summary 用id查询game表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Game true "用id查询game表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /game/findGame [get]
export const findGame = (params) => {
  return service({
    url: '/game/findGame',
    method: 'get',
    params
  })
}

// @Tags Game
// @Summary 分页获取game表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取game表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /game/getGameList [get]
export const getGameList = (params) => {
  return service({
    url: '/game/getGameList',
    method: 'get',
    params
  })
}

// @Tags Game
// @Summary 不需要鉴权的game表接口
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.GameSearch true "分页获取game表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /game/getGamePublic [get]
export const getGamePublic = () => {
  return service({
    url: '/game/getGamePublic',
    method: 'get',
  })
}



export const syncGame = () => {
  return service({
    url: '/game/syncGame',
    method: 'get',
  })
}
