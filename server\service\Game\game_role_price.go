package Game

import (
	"context"
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
)

type GameRolePriceService struct{}

// CreateGameRolePrice 创建gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) CreateGameRolePrice(ctx context.Context, gameRolePrice *Game.GameRolePrice) (err error) {
	err = global.GVA_DB.Create(gameRolePrice).Error
	return err
}

// DeleteGameRolePrice 删除gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) DeleteGameRolePrice(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&Game.GameRolePrice{}, "id = ?", id).Error
	return err
}

// DeleteGameRolePriceByIds 批量删除gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) DeleteGameRolePriceByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]Game.GameRolePrice{}, "id in ?", ids).Error
	return err
}

// UpdateGameRolePrice 更新gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) UpdateGameRolePrice(ctx context.Context, gameRolePrice Game.GameRolePrice) (err error) {
	err = global.GVA_DB.Model(&Game.GameRolePrice{}).Where("id = ?", gameRolePrice.Id).Updates(&gameRolePrice).Error
	return err
}

// GetGameRolePrice 根据id获取gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) GetGameRolePrice(ctx context.Context, id string) (gameRolePrice Game.GameRolePrice, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&gameRolePrice).Error
	return
}

// GetGameRolePriceInfoList 分页获取gameRolePrice表记录
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) GetGameRolePriceInfoList(ctx context.Context, info GameReq.GameRolePriceSearch) (list []Game.GameRolePrice, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&Game.GameRolePrice{})
	var gameRolePrices []Game.GameRolePrice
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.GameId != nil {
		db = db.Where("game_id = ?", *info.GameId)
	}
	if info.RoleName != nil && *info.RoleName != "" {
		db = db.Where("role_name LIKE ?", "%"+*info.RoleName+"%")
	}
	if info.RoleCode != nil && *info.RoleCode != "" {
		db = db.Where("role_code = ?", *info.RoleCode)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&gameRolePrices).Error
	return gameRolePrices, total, err
}
func (gameRolePriceService *GameRolePriceService) GetGameRolePricePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// SyncGameRolePrice
func (gameRolePriceService *GameRolePriceService) SyncGameRolePrice(ctx context.Context, gameId int) (err error) {

	// 从 data_news_item_role 表中获取游戏列表,同步到game表
	// 1. 查询data_news_item表中的游戏数据
	type NewsItem struct {
		Id   int    `json:"id"`
		Name string `json:"name"`
		Code string `json:"code"`
	}
	var newsItems []NewsItem
	err = global.GVA_DB.Table("data_news_item_role").Select([]string{"id", "name", "code"}).Where("item_id", gameId).Find(&newsItems).Error
	if err != nil {
		return err
	}

	// 2. 遍历查询结果，更新或创建game表记录
	for _, item := range newsItems {
		var count int64
		// 检查该游戏是否已存在
		err = global.GVA_DB.Model(&Game.GameRolePrice{}).Where("id = ?", item.Id).Count(&count).Error
		if err != nil {
			return err
		}

		// 构建游戏对象
		game := Game.GameRolePrice{
			Id:       &item.Id,
			GameId:   &gameId,
			RoleName: &item.Name,
			RoleCode: &item.Code,
		}

		if count > 0 {
			// 更新已存在的记录
			err = global.GVA_DB.Model(&Game.GameRolePrice{}).Where("id = ?", item.Id).Updates(&game).Error
		} else {
			// 创建新记录
			err = global.GVA_DB.Create(&game).Error
		}

		if err != nil {
			return err
		}
	}

	return nil
}

// BatchUpdatePrice 批量更新价格
// Author [yourname](https://github.com/yourname)
func (gameRolePriceService *GameRolePriceService) BatchUpdatePrice(ctx context.Context, req GameReq.BatchUpdatePriceReq) (err error) {
	// 参数验证
	if len(req.Ids) == 0 {
		return errors.New("请选择要更新的记录")
	}

	if req.UpdateType != "fixed" && req.UpdateType != "percentage" {
		return errors.New("更新类型只能是 fixed 或 percentage")
	}

	if req.Value <= 0 {
		return errors.New("更新值必须大于0")
	}

	// 根据更新类型处理价格
	switch req.UpdateType {
	case "fixed":
		// 固定值更新：直接设置为指定价格
		err = global.GVA_DB.Model(&Game.GameRolePrice{}).
			Where("id IN ?", req.Ids).
			Update("base_price", req.Value).Error
		if err != nil {
			return err
		}
	case "percentage":
		// 百分比更新：按百分比调整现有价格
		// 200表示2倍(200%)，50表示5折(50%)
		multiplier := req.Value / 100.0

		// 使用原生SQL更新，避免精度问题
		err = global.GVA_DB.Exec(
			"UPDATE game_role_price SET base_price = ROUND(base_price * ?, 2) WHERE id IN ?",
			multiplier, req.Ids,
		).Error
		if err != nil {
			return err
		}
	}

	return nil
}
