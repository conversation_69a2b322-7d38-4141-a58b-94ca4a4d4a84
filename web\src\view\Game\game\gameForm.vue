
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="游戏ID:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入游戏ID" />
</el-form-item>
        <el-form-item label="游戏名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入游戏名称" />
</el-form-item>
        <el-form-item label="游戏代码:" prop="code">
    <el-input v-model="formData.code" :clearable="true" placeholder="请输入游戏代码" />
</el-form-item>
        <el-form-item label="是否启用:" prop="isEnable">
    <el-input v-model.number="formData.isEnable" :clearable="true" placeholder="请输入是否启用" />
</el-form-item>
        <el-form-item label="游戏描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入游戏描述" />
</el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createGame,
  updateGame,
  findGame
} from '@/api/Game/game'

defineOptions({
    name: 'GameForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            id: undefined,
            name: '',
            code: '',
            isEnable: undefined,
            description: '',
            createdAt: new Date(),
            updatedAt: new Date(),
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findGame({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createGame(formData.value)
               break
             case 'update':
               res = await updateGame(formData.value)
               break
             default:
               res = await createGame(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
