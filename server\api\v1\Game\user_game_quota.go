package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserGameQuotaApi struct{}

// CreateUserGameQuota 创建userGameQuota表
// @Tags UserGameQuota
// @Summary 创建userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.UserGameQuota true "创建userGameQuota表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /userGameQuota/createUserGameQuota [post]
func (userGameQuotaApi *UserGameQuotaApi) CreateUserGameQuota(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var userGameQuota Game.UserGameQuota
	err := c.ShouldBindJSON(&userGameQuota)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userGameQuotaService.CreateUserGameQuota(ctx, &userGameQuota)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteUserGameQuota 删除userGameQuota表
// @Tags UserGameQuota
// @Summary 删除userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.UserGameQuota true "删除userGameQuota表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /userGameQuota/deleteUserGameQuota [delete]
func (userGameQuotaApi *UserGameQuotaApi) DeleteUserGameQuota(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := userGameQuotaService.DeleteUserGameQuota(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteUserGameQuotaByIds 批量删除userGameQuota表
// @Tags UserGameQuota
// @Summary 批量删除userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /userGameQuota/deleteUserGameQuotaByIds [delete]
func (userGameQuotaApi *UserGameQuotaApi) DeleteUserGameQuotaByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := userGameQuotaService.DeleteUserGameQuotaByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateUserGameQuota 更新userGameQuota表
// @Tags UserGameQuota
// @Summary 更新userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.UserGameQuota true "更新userGameQuota表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /userGameQuota/updateUserGameQuota [put]
func (userGameQuotaApi *UserGameQuotaApi) UpdateUserGameQuota(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var userGameQuota Game.UserGameQuota
	err := c.ShouldBindJSON(&userGameQuota)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userGameQuotaService.UpdateUserGameQuota(ctx, userGameQuota)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindUserGameQuota 用id查询userGameQuota表
// @Tags UserGameQuota
// @Summary 用id查询userGameQuota表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询userGameQuota表"
// @Success 200 {object} response.Response{data=Game.UserGameQuota,msg=string} "查询成功"
// @Router /userGameQuota/findUserGameQuota [get]
func (userGameQuotaApi *UserGameQuotaApi) FindUserGameQuota(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reuserGameQuota, err := userGameQuotaService.GetUserGameQuota(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reuserGameQuota, c)
}

// GetUserGameQuotaList 分页获取userGameQuota表列表
// @Tags UserGameQuota
// @Summary 分页获取userGameQuota表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.UserGameQuotaSearch true "分页获取userGameQuota表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /userGameQuota/getUserGameQuotaList [get]
func (userGameQuotaApi *UserGameQuotaApi) GetUserGameQuotaList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo GameReq.UserGameQuotaSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userGameQuotaService.GetUserGameQuotaInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetUserGameQuotaPublic 不需要鉴权的userGameQuota表接口
// @Tags UserGameQuota
// @Summary 不需要鉴权的userGameQuota表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /userGameQuota/getUserGameQuotaPublic [get]
func (userGameQuotaApi *UserGameQuotaApi) GetUserGameQuotaPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	userGameQuotaService.GetUserGameQuotaPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的userGameQuota表接口信息",
	}, "获取成功", c)
}

// SyncUserGameQuota syncUserGameQuota
// @Tags UserGameQuota
// @Summary syncUserGameQuota
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.UserGameQuotaSearch true "成功"
// @Success 200 {object} response.Response{data=object,msg=string} "成功"
// @Router /userGameQuota/syncUserGameQuota [GET]
func (userGameQuotaApi *UserGameQuotaApi) SyncUserGameQuota(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()
	// 请添加自己的业务逻辑
	err := userGameQuotaService.SyncUserGameQuota(ctx)
	if err != nil {
		global.GVA_LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData("返回数据", c)
}

// GetUserEfficiency 获取用户额度
// @Tags UserGameQuota
// @Summary 获取用户额度
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.UserGameQuotaSearch true "成功"
// @Success 200 {object} response.Response{data=object,msg=string} "成功"
// @Router /userGameQuota/GetUserEfficiency [GET]
func (userGameQuotaApi *UserGameQuotaApi) GetUserEfficiency(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()
	// 请添加自己的业务逻辑
	username := utils.GetUserName(c)

	data, err := userGameQuotaService.GetUserEfficiency(ctx, username)
	if err != nil {
		global.GVA_LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(data, c)
}
