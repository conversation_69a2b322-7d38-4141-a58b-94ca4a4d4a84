import service from '@/utils/request'
// @Tags GameRolePrice
// @Summary 创建gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.GameRolePrice true "创建gameRolePrice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /gameRolePrice/createGameRolePrice [post]
export const createGameRolePrice = (data) => {
  return service({
    url: '/gameRolePrice/createGameRolePrice',
    method: 'post',
    data
  })
}

// @Tags GameRolePrice
// @Summary 删除gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.GameRolePrice true "删除gameRolePrice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gameRolePrice/deleteGameRolePrice [delete]
export const deleteGameRolePrice = (params) => {
  return service({
    url: '/gameRolePrice/deleteGameRolePrice',
    method: 'delete',
    params
  })
}

// @Tags GameRolePrice
// @Summary 批量删除gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除gameRolePrice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gameRolePrice/deleteGameRolePrice [delete]
export const deleteGameRolePriceByIds = (params) => {
  return service({
    url: '/gameRolePrice/deleteGameRolePriceByIds',
    method: 'delete',
    params
  })
}

// @Tags GameRolePrice
// @Summary 更新gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.GameRolePrice true "更新gameRolePrice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /gameRolePrice/updateGameRolePrice [put]
export const updateGameRolePrice = (data) => {
  return service({
    url: '/gameRolePrice/updateGameRolePrice',
    method: 'put',
    data
  })
}

// @Tags GameRolePrice
// @Summary 用id查询gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.GameRolePrice true "用id查询gameRolePrice表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /gameRolePrice/findGameRolePrice [get]
export const findGameRolePrice = (params) => {
  return service({
    url: '/gameRolePrice/findGameRolePrice',
    method: 'get',
    params
  })
}

// @Tags GameRolePrice
// @Summary 分页获取gameRolePrice表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取gameRolePrice表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /gameRolePrice/getGameRolePriceList [get]
export const getGameRolePriceList = (params) => {
  return service({
    url: '/gameRolePrice/getGameRolePriceList',
    method: 'get',
    params
  })
}

// @Tags GameRolePrice
// @Summary 不需要鉴权的gameRolePrice表接口
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.GameRolePriceSearch true "分页获取gameRolePrice表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /gameRolePrice/getGameRolePricePublic [get]
export const getGameRolePricePublic = () => {
  return service({
    url: '/gameRolePrice/getGameRolePricePublic',
    method: 'get',
  })
}

//syncGameRolePrice
export const syncGameRolePrice = (params) => {
  return service({
    url: '/gameRolePrice/syncGameRolePrice',
    method: 'get',
    params
  })
}

// @Tags GameRolePrice
// @Summary 批量更新gameRolePrice表价格
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body object true "批量更新价格参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量更新成功"}"
// @Router /gameRolePrice/batchUpdatePrice [put]
export const batchUpdatePrice = (data) => {
  return service({
    url: '/gameRolePrice/batchUpdatePrice',
    method: 'put',
    data
  })
}
