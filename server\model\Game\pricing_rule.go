// 自动生成模板PricingRule
package Game

// pricingRule表 结构体  PricingRule
type PricingRule struct {
	Id                    *int     `json:"id" form:"id" gorm:"primarykey;column:id;"size:19;`                                                                  //id字段
	GameId                *int     `json:"gameId" form:"gameId" gorm:"comment:所属游戏ID;column:game_id;"size:19;`                                                 //所属游戏ID
	Status                *int     `json:"status" form:"status" gorm:"default:0;comment:规则状态(1启用,0禁用);column:status;"`                                         //规则状态(1启用,0禁用)
	RoleCodes             *string  `json:"roleCodes" form:"roleCodes" gorm:"comment:指定角色代码列表，逗号分隔;column:role_codes;"size:191;`                                //指定角色代码列表，逗号分隔
	RoleBasePrice         *float64 `json:"roleBasePrice" form:"roleBasePrice" gorm:"comment:指定角色基础价;column:role_base_price;"size:22;`                          //指定角色基础价
	ServerCode            *string  `json:"serverCode" form:"serverCode" gorm:"comment:指定服务器代码;column:server_code;"size:191;`                                   //指定服务器代码
	ServerPremium         *float64 `json:"serverPremium" form:"serverPremium" gorm:"default:100;comment:服务器溢价百分比;column:server_premium;"size:22;`              //服务器溢价百分比
	UnspecifiedMultiplier *float64 `json:"unspecifiedMultiplier" form:"unspecifiedMultiplier" gorm:"comment:未指定角色定价倍率;column:unspecified_multiplier;"size:22;` //未指定角色定价倍率
	QuantityCondition     *string  `json:"quantityCondition" form:"quantityCondition" gorm:"comment:数量条件;column:quantity_condition;"size:191;`                 //数量条件
	DiscountRate          *float64 `json:"discountRate" form:"discountRate" gorm:"default:100;comment:整体折扣率(10%-200%);column:discount_rate;"size:22;`          //整体折扣率(10%-200%)

	// 在结构体中新增如下字段
	Repeats *string `json:"repeats" form:"repeats" gorm:"column:repeats;"` //重复鸡哥

}

// TableName pricingRule表 PricingRule自定义表名 pricing_rule
func (PricingRule) TableName() string {
	return "pricing_rule"
}
