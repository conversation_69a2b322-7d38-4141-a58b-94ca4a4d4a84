package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PricingRuleApi struct{}

// CreatePricingRule 创建pricingRule表
// @Tags PricingRule
// @Summary 创建pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.PricingRule true "创建pricingRule表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /pricingRule/createPricingRule [post]
func (pricingRuleApi *PricingRuleApi) CreatePricingRule(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pricingRule Game.PricingRule
	err := c.ShouldBindJSON(&pricingRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = pricingRuleService.CreatePricingRule(ctx, &pricingRule)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeletePricingRule 删除pricingRule表
// @Tags PricingRule
// @Summary 删除pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.PricingRule true "删除pricingRule表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /pricingRule/deletePricingRule [delete]
func (pricingRuleApi *PricingRuleApi) DeletePricingRule(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := pricingRuleService.DeletePricingRule(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeletePricingRuleByIds 批量删除pricingRule表
// @Tags PricingRule
// @Summary 批量删除pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /pricingRule/deletePricingRuleByIds [delete]
func (pricingRuleApi *PricingRuleApi) DeletePricingRuleByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := pricingRuleService.DeletePricingRuleByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdatePricingRule 更新pricingRule表
// @Tags PricingRule
// @Summary 更新pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.PricingRule true "更新pricingRule表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /pricingRule/updatePricingRule [put]
func (pricingRuleApi *PricingRuleApi) UpdatePricingRule(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var pricingRule Game.PricingRule
	err := c.ShouldBindJSON(&pricingRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = pricingRuleService.UpdatePricingRule(ctx, pricingRule)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindPricingRule 用id查询pricingRule表
// @Tags PricingRule
// @Summary 用id查询pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询pricingRule表"
// @Success 200 {object} response.Response{data=Game.PricingRule,msg=string} "查询成功"
// @Router /pricingRule/findPricingRule [get]
func (pricingRuleApi *PricingRuleApi) FindPricingRule(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	repricingRule, err := pricingRuleService.GetPricingRule(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(repricingRule, c)
}

// GetPricingRuleList 分页获取pricingRule表列表
// @Tags PricingRule
// @Summary 分页获取pricingRule表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.PricingRuleSearch true "分页获取pricingRule表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /pricingRule/getPricingRuleList [get]
func (pricingRuleApi *PricingRuleApi) GetPricingRuleList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo GameReq.PricingRuleSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := pricingRuleService.GetPricingRuleInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetPricingRulePublic 不需要鉴权的pricingRule表接口
// @Tags PricingRule
// @Summary 不需要鉴权的pricingRule表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /pricingRule/getPricingRulePublic [get]
func (pricingRuleApi *PricingRuleApi) GetPricingRulePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	pricingRuleService.GetPricingRulePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的pricingRule表接口信息",
	}, "获取成功", c)
}

// calePricing
func (pricingRuleApi *PricingRuleApi) CalePricing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req GameReq.CalePricingReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := pricingRuleService.CalePricing(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("计算失败!", zap.Error(err))
		response.FailWithMessage("计算失败:"+err.Error(), c)
		return
	}
	response.OkWithData(resp, c)
}
