import service from '@/utils/request'
// @Tags PricingRule
// @Summary 创建pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.PricingRule true "创建pricingRule表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /pricingRule/createPricingRule [post]
export const createPricingRule = (data) => {
  return service({
    url: '/pricingRule/createPricingRule',
    method: 'post',
    data
  })
}

// @Tags PricingRule
// @Summary 删除pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.PricingRule true "删除pricingRule表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /pricingRule/deletePricingRule [delete]
export const deletePricingRule = (params) => {
  return service({
    url: '/pricingRule/deletePricingRule',
    method: 'delete',
    params
  })
}

// @Tags PricingRule
// @Summary 批量删除pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除pricingRule表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /pricingRule/deletePricingRule [delete]
export const deletePricingRuleByIds = (params) => {
  return service({
    url: '/pricingRule/deletePricingRuleByIds',
    method: 'delete',
    params
  })
}

// @Tags PricingRule
// @Summary 更新pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.PricingRule true "更新pricingRule表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /pricingRule/updatePricingRule [put]
export const updatePricingRule = (data) => {
  return service({
    url: '/pricingRule/updatePricingRule',
    method: 'put',
    data
  })
}

// @Tags PricingRule
// @Summary 用id查询pricingRule表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.PricingRule true "用id查询pricingRule表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /pricingRule/findPricingRule [get]
export const findPricingRule = (params) => {
  return service({
    url: '/pricingRule/findPricingRule',
    method: 'get',
    params
  })
}

// @Tags PricingRule
// @Summary 分页获取pricingRule表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取pricingRule表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /pricingRule/getPricingRuleList [get]
export const getPricingRuleList = (params) => {
  return service({
    url: '/pricingRule/getPricingRuleList',
    method: 'get',
    params
  })
}

// @Tags PricingRule
// @Summary 不需要鉴权的pricingRule表接口
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.PricingRuleSearch true "分页获取pricingRule表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /pricingRule/getPricingRulePublic [get]
export const getPricingRulePublic = () => {
  return service({
    url: '/pricingRule/getPricingRulePublic',
    method: 'get',
  })
}


//计算价格
export const calePricing = (params) => {
  return service({
    url: '/pricingRule/calePricing',
    method: 'get',
    params
  })
}

// 批量更新定价规则
export const batchUpdatePricingRule = (data) => {
  return service({
    url: '/pricingRule/batchUpdate',
    method: 'put',
    data
  })
}