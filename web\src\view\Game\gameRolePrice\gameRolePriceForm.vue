
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="所属游戏id:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入所属游戏id" />
</el-form-item>
        <el-form-item label="角色ID（关联data_news_item_role的id）:" prop="roleId">
    <el-input v-model.number="formData.roleId" :clearable="true" placeholder="请输入角色ID（关联data_news_item_role的id）" />
</el-form-item>
        <el-form-item label="角色名称:" prop="roleName">
    <el-input v-model="formData.roleName" :clearable="true" placeholder="请输入角色名称" />
</el-form-item>
        <el-form-item label="角色代码:" prop="roleCode">
    <el-input v-model="formData.roleCode" :clearable="true" placeholder="请输入角色代码" />
</el-form-item>
        <el-form-item label="基础价格:" prop="basePrice">
    <el-input-number v-model="formData.basePrice" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createGameRolePrice,
  updateGameRolePrice,
  findGameRolePrice
} from '@/api/Game/gameRolePrice'

defineOptions({
    name: 'GameRolePriceForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            gameId: undefined,
            roleId: undefined,
            roleName: '',
            roleCode: '',
            basePrice: 0,
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findGameRolePrice({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createGameRolePrice(formData.value)
               break
             case 'update':
               res = await updateGameRolePrice(formData.value)
               break
             default:
               res = await createGameRolePrice(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
