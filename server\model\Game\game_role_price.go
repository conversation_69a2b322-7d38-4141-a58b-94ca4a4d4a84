
// 自动生成模板GameRolePrice
package Game
import (
	"time"
)

// gameRolePrice表 结构体  GameRolePrice
type GameRolePrice struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;column:id;"size:10;`  //id字段
  GameId  *int `json:"gameId" form:"gameId" gorm:"comment:所属游戏id;column:game_id;"size:10;`  //所属游戏id
  RoleId  *int `json:"roleId" form:"roleId" gorm:"comment:角色ID（关联data_news_item_role的id）;column:role_id;"size:19;`  //角色ID（关联data_news_item_role的id）
  RoleName  *string `json:"roleName" form:"roleName" gorm:"comment:角色名称;column:role_name;"size:191;`  //角色名称
  RoleCode  *string `json:"roleCode" form:"roleCode" gorm:"comment:角色代码;column:role_code;"size:191;`  //角色代码
  BasePrice  *float64 `json:"basePrice" form:"basePrice" gorm:"comment:基础价格;column:base_price;"size:22;`  //基础价格
  CreateAt  *time.Time `json:"createAt" form:"createAt" gorm:"comment:创建时间;column:create_at;"`  //创建时间
  UpdateAt  *time.Time `json:"updateAt" form:"updateAt" gorm:"comment:更新时间;column:update_at;"`  //更新时间
}


// TableName gameRolePrice表 GameRolePrice自定义表名 game_role_price
func (GameRolePrice) TableName() string {
    return "game_role_price"
}





