package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DeliveryrecordRouter struct{}

// InitDeliveryrecordRouter 初始化 提取记录 路由信息
func (s *DeliveryrecordRouter) InitDeliveryrecordRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	deliveryrecordRouter := Router.Group("deliveryrecord").Use(middleware.OperationRecord())
	deliveryrecordRouterWithoutRecord := Router.Group("deliveryrecord")
	deliveryrecordRouterWithoutAuth := PublicRouter.Group("deliveryrecord")
	{
		deliveryrecordRouter.POST("createDeliveryrecord", deliveryrecordApi.CreateDeliveryrecord)             // 新建提取记录
		deliveryrecordRouter.DELETE("deleteDeliveryrecord", deliveryrecordApi.DeleteDeliveryrecord)           // 删除提取记录
		deliveryrecordRouter.DELETE("deleteDeliveryrecordByIds", deliveryrecordApi.DeleteDeliveryrecordByIds) // 批量删除提取记录
		deliveryrecordRouter.PUT("updateDeliveryrecord", deliveryrecordApi.UpdateDeliveryrecord)              // 更新提取记录
	}
	{
		deliveryrecordRouterWithoutRecord.GET("findDeliveryrecord", deliveryrecordApi.FindDeliveryrecord)       // 根据ID获取提取记录
		deliveryrecordRouterWithoutRecord.GET("getDeliveryrecordList", deliveryrecordApi.GetDeliveryrecordList) // 获取提取记录列表
		//findaccount
		deliveryrecordRouterWithoutRecord.GET("findaccount", deliveryrecordApi.FindAccount) // 获取用户列表
		//pickaccount
		deliveryrecordRouterWithoutRecord.GET("pickaccount", deliveryrecordApi.PickAccount) // 提取账号
	}
	{
		deliveryrecordRouterWithoutAuth.GET("getDeliveryrecordPublic", deliveryrecordApi.GetDeliveryrecordPublic) // 提取记录开放接口
	}
}
