package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type GameRolePriceSearch struct {
	GameId   *int    `json:"gameId" form:"gameId" `
	RoleName *string `json:"roleName" form:"roleName" `
	RoleCode *string `json:"roleCode" form:"roleCode" `
	request.PageInfo
}

// BatchUpdatePriceReq 批量更新价格请求结构体
type BatchUpdatePriceReq struct {
	Ids        []int   `json:"ids" binding:"required"`        // 要更新的记录ID列表
	UpdateType string  `json:"updateType" binding:"required"` // 更新类型：fixed(固定值) 或 percentage(百分比)
	Value      float64 `json:"value" binding:"required"`      // 更新值：固定价格或百分比(如200表示2倍，50表示5折)
}
