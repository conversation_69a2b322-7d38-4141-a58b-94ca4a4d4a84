
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="主键ID:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入主键ID" />
</el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="删除时间:" prop="deletedAt">
    <el-date-picker v-model="formData.deletedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="用户UUID:" prop="userUuid">
    <el-input v-model="formData.userUuid" :clearable="true" placeholder="请输入用户UUID" />
</el-form-item>
        <el-form-item label="用户登录名:" prop="username">
    <el-input v-model="formData.username" :clearable="true" placeholder="请输入用户登录名" />
</el-form-item>
        <el-form-item label="用户昵称:" prop="nickName">
    <el-input v-model="formData.nickName" :clearable="true" placeholder="请输入用户昵称" />
</el-form-item>
        <el-form-item label="游戏ID:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入游戏ID" />
</el-form-item>
        <el-form-item label="总额度:" prop="totalQuota">
    <el-input v-model.number="formData.totalQuota" :clearable="true" placeholder="请输入总额度" />
</el-form-item>
        <el-form-item label="已使用额度:" prop="usedQuota">
    <el-input v-model.number="formData.usedQuota" :clearable="true" placeholder="请输入已使用额度" />
</el-form-item>
        <el-form-item label="每日限额:" prop="dailyLimit">
    <el-input v-model.number="formData.dailyLimit" :clearable="true" placeholder="请输入每日限额" />
</el-form-item>
        <el-form-item label="今日已用:" prop="todayUsed">
    <el-input v-model.number="formData.todayUsed" :clearable="true" placeholder="请输入今日已用" />
</el-form-item>
        <el-form-item label="上次重置日期:" prop="lastResetDate">
    <el-date-picker v-model="formData.lastResetDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="额度过期时间:" prop="expirationDate">
    <el-date-picker v-model="formData.expirationDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="状态(1正常,0禁用):" prop="status">
    <el-input v-model.number="formData.status" :clearable="true" placeholder="请输入状态(1正常,0禁用)" />
</el-form-item>
        <el-form-item label="备注:" prop="remark">
    <el-input v-model="formData.remark" :clearable="true" placeholder="请输入备注" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createUserGameQuota,
  updateUserGameQuota,
  findUserGameQuota
} from '@/api/Game/userGameQuota'

defineOptions({
    name: 'UserGameQuotaForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            id: undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            userUuid: '',
            username: '',
            nickName: '',
            gameId: undefined,
            totalQuota: undefined,
            usedQuota: undefined,
            dailyLimit: undefined,
            todayUsed: undefined,
            lastResetDate: new Date(),
            expirationDate: new Date(),
            status: undefined,
            remark: '',
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findUserGameQuota({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createUserGameQuota(formData.value)
               break
             case 'update':
               res = await updateUserGameQuota(formData.value)
               break
             default:
               res = await createUserGameQuota(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
