
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
            <el-form-item label="所属游戏ID" prop="gameId">
  <el-input v-model.number="searchInfo.gameId" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="角色代码" prop="code">
  <el-input v-model="searchInfo.code" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="价格" prop="price">
  <el-input v-model.number="searchInfo.price" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="用户" prop="user">
  <el-input v-model="searchInfo.user" placeholder="搜索条件" />
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <!-- <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button> -->
            <!-- <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button> -->
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        >
        <el-table-column type="selection" width="55" />

            <el-table-column align="left" label="id字段" prop="id" width="120" sortable="custom" />

            <el-table-column align="left" label="所属游戏ID" prop="gameId" width="120" sortable="custom" />

            <el-table-column align="left" label="角色代码" prop="code" width="120" />

            <el-table-column align="left" label="价格" prop="price" width="120" sortable="custom" />

            <el-table-column align="left" label="用户" prop="user" width="120" />

            <el-table-column align="left" label="账号文本" prop="info" width="250">
              <template #default="scope">
                <div class="info-cell">
                  <div class="info-text" :title="scope.row.info">
                    {{ scope.row.info }}
                  </div>
                  <el-button
                    v-if="scope.row.info && scope.row.info.length > 50"
                    type="text"
                    size="small"
                    @click="showFullInfo(scope.row.info)"
                    class="view-full-btn">
                    查看全部
                  </el-button>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="left" label="提取时间" prop="pickDate" width="180" sortable="custom">
   <template #default="scope">{{ formatDate(scope.row.pickDate) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <!-- <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateDeliveryrecordFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button> -->
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="id字段:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入id字段" />
</el-form-item>
            <el-form-item label="所属游戏ID:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入所属游戏ID" />
</el-form-item>
            <el-form-item label="角色代码:" prop="code">
    <el-input v-model="formData.code" :clearable="true" placeholder="请输入角色代码" />
</el-form-item>
            <el-form-item label="价格:" prop="price">
    <el-input v-model.number="formData.price" :clearable="true" placeholder="请输入价格" />
</el-form-item>
            <el-form-item label="用户:" prop="user">
    <el-input v-model="formData.user" :clearable="true" placeholder="请输入用户" />
</el-form-item>
            <el-form-item label="提取时间:" prop="pickDate">
    <el-date-picker v-model="formData.pickDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="id字段">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="所属游戏ID">
    {{ detailFrom.gameId }}
</el-descriptions-item>
                    <el-descriptions-item label="角色代码">
    {{ detailFrom.code }}
</el-descriptions-item>
                    <el-descriptions-item label="价格">
    {{ detailFrom.price }}
</el-descriptions-item>
                    <el-descriptions-item label="用户">
    {{ detailFrom.user }}
</el-descriptions-item>
                    <el-descriptions-item label="提取时间">
    {{ detailFrom.pickDate }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

    <!-- 查看完整账号文本弹窗 -->
    <el-dialog v-model="fullInfoVisible" title="完整账号文本" width="600px" :before-close="closeFullInfo">
      <div class="full-info-content">
        <el-input
          v-model="fullInfoText"
          type="textarea"
          :rows="10"
          readonly
          resize="none"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyFullInfo">复制</el-button>
          <el-button type="primary" @click="closeFullInfo">关闭</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import {
  createDeliveryrecord,
  deleteDeliveryrecord,
  deleteDeliveryrecordByIds,
  updateDeliveryrecord,
  findDeliveryrecord,
  getDeliveryrecordList
} from '@/api/Game/deliveryrecord'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'Deliveryrecord'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            id: undefined,
            gameId: undefined,
            code: '',
            price: undefined,
            user: '',
            pickDate: new Date(),
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 处理表格排序变化
const handleSortChange = ({ column, prop, order }) => {
  // 将 Element Plus 的排序字段映射到后端字段
  const sortFieldMap = {
    'id': 'id',
    'gameId': 'game_id',
    'price': 'price',
    'pickDate': 'pick_date'
  }

  if (prop && sortFieldMap[prop]) {
    searchInfo.value.sort = sortFieldMap[prop]
    searchInfo.value.order = order || 'descending' // 默认降序
  } else {
    // 清除排序
    searchInfo.value.sort = 'id'
    searchInfo.value.order = 'descending'
  }

  page.value = 1 // 重置到第一页
  getTableData()
}

// 查询
const getTableData = async() => {
  // 构建查询参数，包含排序信息
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value
  }

  // 如果没有设置排序，使用默认排序（按ID降序，最新记录在前）
  if (!params.sort) {
    params.sort = 'id'
    params.order = 'descending'
  }

  const table = await getDeliveryrecordList(params)
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteDeliveryrecordFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteDeliveryrecordByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateDeliveryrecordFunc = async(row) => {
    const res = await findDeliveryrecord({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteDeliveryrecordFunc = async (row) => {
    const res = await deleteDeliveryrecord({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        gameId: undefined,
        code: '',
        price: undefined,
        user: '',
        pickDate: new Date(),
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createDeliveryrecord(formData.value)
                  break
                case 'update':
                  res = await updateDeliveryrecord(formData.value)
                  break
                default:
                  res = await createDeliveryrecord(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findDeliveryrecord({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 完整信息弹窗相关
const fullInfoVisible = ref(false)
const fullInfoText = ref('')

// 显示完整账号文本
const showFullInfo = (info) => {
  fullInfoText.value = info || ''
  fullInfoVisible.value = true
}

// 关闭完整信息弹窗
const closeFullInfo = () => {
  fullInfoVisible.value = false
  fullInfoText.value = ''
}

// 复制完整信息
const copyFullInfo = () => {
  navigator.clipboard.writeText(fullInfoText.value)
    .then(() => {
      ElMessage.success('复制成功')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}


</script>

<style scoped>
/* 账号文本单元格样式 */
.info-cell {
  position: relative;
  max-height: 60px;
  overflow: hidden;
}

.info-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  line-clamp: 2; /* 标准属性 */
  -webkit-box-orient: vertical;
  word-break: break-all;
  line-height: 1.4;
  margin-bottom: 4px;
  font-size: 13px;
}

.view-full-btn {
  padding: 0 4px;
  height: 20px;
  font-size: 11px;
  color: #409eff;
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}

.view-full-btn:hover {
  color: #66b1ff;
}

/* 表格行高控制 */
:deep(.el-table .el-table__row) {
  height: auto;
  min-height: 50px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
  vertical-align: top;
}

/* 完整信息弹窗样式 */
.full-info-content {
  margin: 10px 0;
}

.full-info-content .el-textarea__inner {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}
</style>
