<!-- 输入编号,查询 服务器,账号资源,账号文本 -->
<template>
    <div>
        <el-row :gutter="20" class="mb-4">
            <el-col :span="8">
                <el-card shadow="hover">
                    <div class="stat-title">今日销量</div>
                    <div class="stat-value">{{ todaySales }}</div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card shadow="hover">
                    <div class="stat-title">本月销量</div>
                    <div class="stat-value">{{ monthSales }}</div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card shadow="hover">
                    <div class="stat-title">使用额度/总额度</div>
                    <div class="stat-value">{{ usedQuota }} / {{ totalQuota }}</div>
                </el-card>
            </el-col>
        </el-row>

        <!-- <div id="salesChart" style="width: 100%; height: 400px;"></div> -->
    </div>
    <div>
        <el-input v-model="ruleNum" placeholder="输入编号" />
        <el-button type="primary" @click="queryAccount">查询</el-button>
        <el-button type="primary" @click="pickAccount">提取</el-button>
    </div>

    <!-- 显示查询到的结果 -->
    <div>
        <div>
            <el-card shadow="hover" class="result-card">
                <template #header>
                    <div class="card-header">
                        <span>账号查询结果</span>
                        <el-tag :type="loading ? 'info' : 'success'">
                            {{ loading ? '加载中' : '查询' }}
                        </el-tag>
                    </div>
                </template>

                <el-skeleton :loading="loading" animated>
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="游戏名称">
                          <el-space>
                              <el-button
                              size="small"
                              @click="copySnippet(accountInfo.game)"
                              :disabled="!accountInfo.game"
                              icon="CopyDocument"
                              />
                              <span>{{ accountInfo.game }}</span>
                          </el-space>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="服务器">
                          <el-space>
                              <el-button
                              size="small"
                              @click="copySnippet(accountInfo.district)"
                              :disabled="!accountInfo.serdistrictver"
                              icon="CopyDocument"
                              />
                              <span>{{ accountInfo.district }}</span>
                          </el-space>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="账号资源">
                          <el-space>
                              <el-button
                              size="small"
                              @click="copySnippet(accountInfo.accountResources)"
                              :disabled="!accountInfo.accountResources"
                              icon="CopyDocument"
                              />
                              <span>{{ accountInfo.accountResources }}</span>
                          </el-space>
                        </el-descriptions-item>

                               <el-descriptions-item label="定价">
                          <el-space>
                              <el-button
                              size="small"
                              @click="copySnippet(accountInfo.price)"
                              :disabled="!accountInfo.price"
                              icon="CopyDocument"
                              />
                              <span>{{ accountInfo.price }}</span>
                          </el-space>
                        </el-descriptions-item>
                        
                        <el-descriptions-item label="账号文本">
                          <el-space>
                              <el-button
                              size="small"
                              @click="copySnippet(accountInfo.accountText)"
                              :disabled="!accountInfo.accountText"
                              icon="CopyDocument"
                              />
                              <span>{{ accountInfo.accountText }}</span>
                          </el-space>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-skeleton>
            </el-card>
        </div>
    </div>
</template>



<script setup>
import {
    _queryAccount,
    _pickAccount
} from './account.js'

import { calePricing } from '@/api/Game/pricingRule'
import { GetUserEfficiency } from '@/api/Game/userGameQuota'
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

const ruleNum = ref('')

var accountInfo = ref({
    game: '',
    district: '',
    accountResources: '',
    accountText: ''
})

const resetAccountInfo = () => {
    accountInfo.value = {
        game: '',
        district: '',
        accountResources: '',
        accountText: '',
        price: ""
    }
}

const todaySales = ref(0)
const monthSales = ref(0)
const usedQuota = ref(3500)
const totalQuota = ref(10000)


const loading = ref(false)
const pickLoading = ref(false)
const pickQuota = ref(0)
const pickQuotaError = ref('')
const pickQuotaSuccess = ref('')

const queryAccount = async () => {
    if (loading.value) return
    loading.value = true
    //清空之前查询过的账号信息
    resetAccountInfo()
    try {
        const response = await _queryAccount({ roleNum: ruleNum.value })
        if (response.code === 0) {
            accountInfo.value.game = response.data.item_name
            accountInfo.value.district = response.data.district
            accountInfo.value.accountResources = response.data.user_role
            accountInfo.value.price = response.data.price

        } else {
            resetAccountInfo()
        }
    } catch (error) {
       resetAccountInfo ()
    } finally {
        loading.value = false
    }
}

const pickAccount = async () => {
//确认框,提示信息是否提取
var msg = "此类组合已被定价【"+accountInfo.value.price+"】如果接受请点确定，不接受请点取消或X取消不会记录价格，不影响销售"
    const confirm = await ElMessageBox.confirm(msg+'确认提取吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
    if (!confirm) {
        return
    }

    if (pickLoading.value) return
    pickLoading.value = true
    pickQuotaError.value = ''
    pickQuotaSuccess.value = ''

    try {
        const response = await _pickAccount({ roleNum: ruleNum.value, price: accountInfo.value.price })
        if (response.code === 0) {
            pickQuotaSuccess.value = '提取成功'
            usedQuota.value += pickQuota.value
            accountInfo.value.accountText = response.data.accountText

        } else {
            pickQuotaError.value = response.data.message
        }
    } catch (error) {
        //如果返回的信息里面有 总额度不足 则弹窗提示  额度不足,联系客服处理
        if (error.message.indexOf('总额度不足') !== -1) {
            ElMessageBox.alert('额度不足,请联系客服处理', '提示', {
                confirmButtonText: '确定',
            })
        }
        pickQuotaError.value = '提取失败，请重试'
    } finally {
        pickLoading.value = false
    }
}


const fetchSalesData = async () => {
    try {
        const response = await GetUserEfficiency()
//{    "code": 0,    "data": {        "monthAmount": 0,        "monthCount": 0,        "monthEfficiency": 0,        "quotaUsageRate": 0,        "todayAmount": 0,        "todayCount": 0,        "todayEfficiency": 0,        "totalQuota": 1000000,        "usedQuota": 0    },    "msg": "成功"}
        console.log(response.data)
        todaySales.value = response.data.todayCount
        monthSales.value = response.data.monthCount
        usedQuota.value = response.data.usedQuota
        totalQuota.value = response.data.totalQuota
        //updateChart(response.data.chart)
    } catch (error) {
        console.error('获取销售数据失败:', error)
    }
}

const updateChart = (data) => {
    const chart = echarts.init(document.getElementById('salesChart'))
    const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
            type: 'category',
            data: data.dates
        },
        yAxis: { type: 'value' },
        series: [{
            data: data.values,
            type: 'line',
            smooth: true,
            areaStyle: {}
        }]
    }
    chart.setOption(option)
}

onMounted(() => {
    //initChart()
    fetchSalesData()
})

const initChart = () => {
    const chart = echarts.init(document.getElementById('salesChart'))
    const option = {
        xAxis: {
            type: 'category',
            data: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7']
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true
        }]
    }
    chart.setOption(option)
}

// 统一复制方法
const copySnippet = (text) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      ElMessage.success('复制成功')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}
</script>

<style scoped>
.result-card {
    margin-top: 1.5rem;
    transition: box-shadow 0.3s;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.el-descriptions__body {
    background: #f8f9fa;
}

.el-descriptions-item__label {
    color: #6c757d;
    font-weight: 500;
}

.el-descriptions-item__content {
    color: #212529;
    font-size: 16px;
}

#salesChart {
    margin-top: 2rem;
}

.stat-title {
    color: #888;
    font-size: 14px;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
}

.mb-4 {
    margin-bottom: 1rem;
}

#salesChart {
    margin-top: 2rem;
}
</style>



