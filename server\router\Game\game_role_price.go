package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type GameRolePriceRouter struct{}

// InitGameRolePriceRouter 初始化 gameRolePrice表 路由信息
func (s *GameRolePriceRouter) InitGameRolePriceRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	gameRolePriceRouter := Router.Group("gameRolePrice").Use(middleware.OperationRecord())
	gameRolePriceRouterWithoutRecord := Router.Group("gameRolePrice")
	gameRolePriceRouterWithoutAuth := PublicRouter.Group("gameRolePrice")
	{
		gameRolePriceRouter.POST("createGameRolePrice", gameRolePriceApi.CreateGameRolePrice)             // 新建gameRolePrice表
		gameRolePriceRouter.DELETE("deleteGameRolePrice", gameRolePriceApi.DeleteGameRolePrice)           // 删除gameRolePrice表
		gameRolePriceRouter.DELETE("deleteGameRolePriceByIds", gameRolePriceApi.DeleteGameRolePriceByIds) // 批量删除gameRolePrice表
		gameRolePriceRouter.PUT("updateGameRolePrice", gameRolePriceApi.UpdateGameRolePrice)              // 更新gameRolePrice表
		gameRolePriceRouter.PUT("batchUpdatePrice", gameRolePriceApi.BatchUpdatePrice)                    // 批量更新价格
	}
	{
		gameRolePriceRouterWithoutRecord.GET("findGameRolePrice", gameRolePriceApi.FindGameRolePrice)       // 根据ID获取gameRolePrice表
		gameRolePriceRouterWithoutRecord.GET("getGameRolePriceList", gameRolePriceApi.GetGameRolePriceList) // 获取gameRolePrice表列表
		gameRolePriceRouterWithoutRecord.GET("syncGameRolePrice", gameRolePriceApi.SyncGameRolePrice)       // 同步游戏角色
	}
	{
		gameRolePriceRouterWithoutAuth.GET("getGameRolePricePublic", gameRolePriceApi.GetGameRolePricePublic) // gameRolePrice表开放接口
	}
}
