package Game

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type GameRouter struct{}

// InitGameRouter 初始化 game表 路由信息
func (s *GameRouter) InitGameRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	gameRouter := Router.Group("game").Use(middleware.OperationRecord())
	gameRouterWithoutRecord := Router.Group("game")
	gameRouterWithoutAuth := PublicRouter.Group("game")
	{
		gameRouter.POST("createGame", gameApi.CreateGame)             // 新建game表
		gameRouter.DELETE("deleteGame", gameApi.DeleteGame)           // 删除game表
		gameRouter.DELETE("deleteGameByIds", gameApi.DeleteGameByIds) // 批量删除game表
		gameRouter.PUT("updateGame", gameApi.UpdateGame)              // 更新game表
	}
	{
		gameRouterWithoutRecord.GET("findGame", gameApi.FindGame)       // 根据ID获取game表
		gameRouterWithoutRecord.GET("getGameList", gameApi.GetGameList) // 获取game表列表
		gameRouterWithoutRecord.GET("syncGame", gameApi.SyncGame)       // 同步游戏列表
	}
	{
		gameRouterWithoutAuth.GET("getGamePublic", gameApi.GetGamePublic) // game表开放接口
	}
}
