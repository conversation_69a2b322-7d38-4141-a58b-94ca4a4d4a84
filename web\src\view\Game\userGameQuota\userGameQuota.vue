
<template>
  <div>
    <!-- 统计信息卡片 -->
    <div class="statistics-container">
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-item">
              <div class="statistics-icon today-sales">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="statistics-content">
                <div class="statistics-title">今日销量</div>
                <div class="statistics-value">{{ statisticsData.todaySales || 0 }}</div>
                <div class="statistics-subtitle">记录数量</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-item">
              <div class="statistics-icon today-quota">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="statistics-content">
                <div class="statistics-title">今日使用额度</div>
                <div class="statistics-value">{{ statisticsData.todayUsedQuota || 0 }}</div>
                <div class="statistics-subtitle">总额度</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-item">
              <div class="statistics-icon month-sales">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="statistics-content">
                <div class="statistics-title">月销量</div>
                <div class="statistics-value">{{ statisticsData.monthSales || 0 }}</div>
                <div class="statistics-subtitle">记录数量</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="statistics-item">
              <div class="statistics-icon month-quota">
                <el-icon><Money /></el-icon>
              </div>
              <div class="statistics-content">
                <div class="statistics-title">月使用额度</div>
                <div class="statistics-value">{{ statisticsData.monthUsedQuota || 0 }}</div>
                <div class="statistics-subtitle">总额度</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
            <el-form-item label="用户登录名" prop="username">
  <el-input v-model="searchInfo.username" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="游戏ID" prop="gameId">
  <el-input v-model.number="searchInfo.gameId" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="总额度" prop="totalQuota">
  <el-input v-model.number="searchInfo.totalQuota" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="已使用额度" prop="usedQuota">
  <el-input v-model.number="searchInfo.usedQuota" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="每日限额" prop="dailyLimit">
  <el-input v-model.number="searchInfo.dailyLimit" placeholder="搜索条件" />
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  type="primary" icon="plus" @click=" syncUserGameQuotaList()">同步用户</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />
        
            <!-- <el-table-column align="left" label="主键ID" prop="id" width="120" /> -->

            <!-- <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
</el-table-column> -->
            <!-- <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="删除时间" prop="deletedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.deletedAt) }}</template>
</el-table-column> -->
            <!-- <el-table-column align="left" label="用户UUID" prop="userUuid" width="120" /> -->

            <el-table-column align="left" label="用户登录名" prop="username" width="120" />

            <!-- <el-table-column align="left" label="用户昵称" prop="nickName" width="120" /> -->

            <!-- <el-table-column align="left" label="游戏ID" prop="gameId" width="120" /> -->

            <el-table-column sortable align="left" label="总额度" prop="totalQuota" width="120" />

            <el-table-column sortable align="left" label="已使用额度" prop="usedQuota" width="120" />

            <!-- <el-table-column sortable align="left" label="每日限额" prop="dailyLimit" width="120" /> -->

            <el-table-column sortable align="left" label="今日已用" prop="todayUsed" width="120" />

            <!-- <el-table-column align="left" label="上次重置日期" prop="lastResetDate" width="180">
   <template #default="scope">{{ formatDate(scope.row.lastResetDate) }}</template>
</el-table-column>
            <el-table-column align="left" label="额度过期时间" prop="expirationDate" width="180">
   <template #default="scope">{{ formatDate(scope.row.expirationDate) }}</template>
</el-table-column> -->
            <el-table-column sortable align="left" label="状态(1正常,0禁用)" prop="status" width="120" />

            <el-table-column align="left" label="备注" prop="remark" width="120" />

        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateUserGameQuotaFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <!-- <el-form-item label="主键ID:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入主键ID" />
</el-form-item>
            <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="删除时间:" prop="deletedAt">
    <el-date-picker v-model="formData.deletedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item> -->
            <!-- <el-form-item label="用户UUID:" prop="userUuid">
    <el-input v-model="formData.userUuid" :clearable="true" placeholder="请输入用户UUID" />
</el-form-item> -->
            <el-form-item label="用户登录名:" prop="username">
    <el-input v-model="formData.username" :clearable="true" placeholder="请输入用户登录名" />
</el-form-item>
            <!-- <el-form-item label="用户昵称:" prop="nickName">
    <el-input v-model="formData.nickName" :clearable="true" placeholder="请输入用户昵称" />
</el-form-item> -->
            <!-- <el-form-item label="游戏ID:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入游戏ID" />
</el-form-item> -->
            <el-form-item label="总额度:" prop="totalQuota">
    <el-input v-model.number="formData.totalQuota" :clearable="true" placeholder="请输入总额度" />
</el-form-item>
            <el-form-item label="已使用额度:" prop="usedQuota">
    <el-input v-model.number="formData.usedQuota" :clearable="true" placeholder="请输入已使用额度" />
</el-form-item>
            <!-- <el-form-item label="每日限额:" prop="dailyLimit">
    <el-input v-model.number="formData.dailyLimit" :clearable="true" placeholder="请输入每日限额" />
</el-form-item>
            <el-form-item label="今日已用:" prop="todayUsed">
    <el-input v-model.number="formData.todayUsed" :clearable="true" placeholder="请输入今日已用" />
</el-form-item>
            <el-form-item label="上次重置日期:" prop="lastResetDate">
    <el-date-picker v-model="formData.lastResetDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="额度过期时间:" prop="expirationDate">
    <el-date-picker v-model="formData.expirationDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item> -->
            <el-form-item label="状态(1正常,0禁用):" prop="status">
    <el-input v-model.number="formData.status" :clearable="true" placeholder="请输入状态(1正常,0禁用)" />
</el-form-item>
            <el-form-item label="备注:" prop="remark">
    <el-input v-model="formData.remark" :clearable="true" placeholder="请输入备注" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <!-- <el-descriptions-item label="主键ID">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.createdAt }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updatedAt }}
</el-descriptions-item>
                    <el-descriptions-item label="删除时间">
    {{ detailFrom.deletedAt }}
</el-descriptions-item>
                    <el-descriptions-item label="用户UUID">
    {{ detailFrom.userUuid }}
</el-descriptions-item> -->
                    <el-descriptions-item label="用户登录名">
    {{ detailFrom.username }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="用户昵称">
    {{ detailFrom.nickName }}
</el-descriptions-item> -->
                    <!-- <el-descriptions-item label="游戏ID">
    {{ detailFrom.gameId }}
</el-descriptions-item> -->
                    <el-descriptions-item label="总额度">
    {{ detailFrom.totalQuota }}
</el-descriptions-item>
                    <el-descriptions-item label="已使用额度">
    {{ detailFrom.usedQuota }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="每日限额">
    {{ detailFrom.dailyLimit }}
</el-descriptions-item>
                    <el-descriptions-item label="今日已用">
    {{ detailFrom.todayUsed }}
</el-descriptions-item>
                    <el-descriptions-item label="上次重置日期">
    {{ detailFrom.lastResetDate }}
</el-descriptions-item>
                    <el-descriptions-item label="额度过期时间">
    {{ detailFrom.expirationDate }}
</el-descriptions-item> -->
                    <el-descriptions-item label="状态(1正常,0禁用)">
    {{ detailFrom.status }}
</el-descriptions-item>
                    <el-descriptions-item label="备注">
    {{ detailFrom.remark }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createUserGameQuota,
  deleteUserGameQuota,
  deleteUserGameQuotaByIds,
  updateUserGameQuota,
  findUserGameQuota,
  getUserGameQuotaList,
  syncUserGameQuota,
  getUserGameQuotaStatistics
} from '@/api/Game/userGameQuota'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { TrendCharts, Wallet, DataAnalysis, Money } from '@element-plus/icons-vue'
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'UserGameQuota'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 统计数据
const statisticsData = ref({
  todaySales: 0,        // 今日销量(记录数量)
  todayUsedQuota: 0,    // 今日使用额度
  monthSales: 0,        // 月销量
  monthUsedQuota: 0     // 月使用额度
})

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            id: undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
            userUuid: '',
            username: '',
            nickName: '',
            gameId: undefined,
            totalQuota: undefined,
            usedQuota: undefined,
            dailyLimit: undefined,
            todayUsed: undefined,
            lastResetDate: new Date(),
            expirationDate: new Date(),
            status: undefined,
            remark: '',
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            totalQuota: 'total_quota',
            usedQuota: 'used_quota',
            dailyLimit: 'daily_limit',
            todayUsed: 'today_used',
            status: 'status',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getUserGameQuotaList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteUserGameQuotaFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteUserGameQuotaByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateUserGameQuotaFunc = async(row) => {
    const res = await findUserGameQuota({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteUserGameQuotaFunc = async (row) => {
    const res = await deleteUserGameQuota({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

//syncUserGameQuotaList
const syncUserGameQuotaList = async () => {
    const res = await syncUserGameQuota()
    if (res.code === 0) {
        ElMessage({
            type: 'success',
            message: '同步成功'
        })
        getTableData()
    }
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: new Date(),
        userUuid: '',
        username: '',
        nickName: '',
        gameId: undefined,
        totalQuota: undefined,
        usedQuota: undefined,
        dailyLimit: undefined,
        todayUsed: undefined,
        lastResetDate: new Date(),
        expirationDate: new Date(),
        status: undefined,
        remark: '',
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createUserGameQuota(formData.value)
                  break
                case 'update':
                  res = await updateUserGameQuota(formData.value)
                  break
                default:
                  res = await createUserGameQuota(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findUserGameQuota({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 获取统计数据
const getStatisticsData = async () => {
  try {
    const res = await getUserGameQuotaStatistics()
    if (res.code === 0) {
      statisticsData.value = {
        todaySales: res.data.todaySales || 0,
        todayUsedQuota: res.data.todayUsedQuota || 0,
        monthSales: res.data.monthSales || 0,
        monthUsedQuota: res.data.monthUsedQuota || 0
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 设置默认值
    statisticsData.value = {
      todaySales: 0,
      todayUsedQuota: 0,
      monthSales: 0,
      monthUsedQuota: 0
    }
  }
}

// 页面挂载时初始化数据
onMounted(() => {
  getStatisticsData()
})

// 重写getTableData方法，在获取表格数据时也刷新统计数据
const originalGetTableData = getTableData
const getTableDataWithStats = async () => {
  await originalGetTableData()
  await getStatisticsData()
}

// 替换原来的getTableData方法
getTableData = getTableDataWithStats


</script>

<style scoped>
/* 统计信息容器样式 */
.statistics-container {
  margin-bottom: 20px;
}

.statistics-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statistics-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.statistics-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.statistics-icon.today-sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.statistics-icon.today-quota {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.statistics-icon.month-sales {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.statistics-icon.month-quota {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.statistics-content {
  flex: 1;
}

.statistics-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.statistics-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  line-height: 1;
}

.statistics-subtitle {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-value {
    font-size: 24px;
  }

  .statistics-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .statistics-item {
    flex-direction: column;
    text-align: center;
  }

  .statistics-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .statistics-value {
    font-size: 20px;
  }
}
</style>
