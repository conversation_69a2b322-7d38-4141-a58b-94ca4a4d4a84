
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="id字段:" prop="id">
    <el-input v-model.number="formData.id" :clearable="true" placeholder="请输入id字段" />
</el-form-item>
        <el-form-item label="所属游戏ID:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入所属游戏ID" />
</el-form-item>
        <el-form-item label="角色代码:" prop="code">
    <el-input v-model="formData.code" :clearable="true" placeholder="请输入角色代码" />
</el-form-item>
        <el-form-item label="价格:" prop="price">
    <el-input v-model.number="formData.price" :clearable="true" placeholder="请输入价格" />
</el-form-item>
        <el-form-item label="用户:" prop="user">
    <el-input v-model="formData.user" :clearable="true" placeholder="请输入用户" />
</el-form-item>
        <el-form-item label="提取时间:" prop="pickDate">
    <el-date-picker v-model="formData.pickDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createDeliveryrecord,
  updateDeliveryrecord,
  findDeliveryrecord
} from '@/api/Game/deliveryrecord'

defineOptions({
    name: 'DeliveryrecordForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            id: undefined,
            gameId: undefined,
            code: '',
            price: undefined,
            user: '',
            pickDate: new Date(),
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findDeliveryrecord({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createDeliveryrecord(formData.value)
               break
             case 'update':
               res = await updateDeliveryrecord(formData.value)
               break
             default:
               res = await createDeliveryrecord(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
