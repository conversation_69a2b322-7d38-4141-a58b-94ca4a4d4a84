package utils

import (
	"fmt"
	"testing"
)

func TestGetJSO<PERSON><PERSON><PERSON><PERSON>(t *testing.T) {
	var jsonStr = `
	{
		"Name": "test",
		"TableName": "test",
		"TemplateID": "test",
		"TemplateInfo": "test",
		"Limit": 0
}`
	keys, err := GetJSONKeys(jsonStr)
	if err != nil {
		t.<PERSON>("GetJSO<PERSON><PERSON><PERSON><PERSON> failed" + err.<PERSON><PERSON><PERSON>())
		return
	}
	if len(keys) != 5 {
		t.<PERSON>("GetJSONK<PERSON>s failed" + err.<PERSON><PERSON><PERSON>())
		return
	}
	if keys[0] != "Name" {
		t.<PERSON><PERSON>("GetJSON<PERSON><PERSON><PERSON> failed" + err.<PERSON><PERSON><PERSON>())

		return
	}
	if keys[1] != "TableName" {
		t.<PERSON>("GetJSON<PERSON><PERSON><PERSON> failed" + err.<PERSON><PERSON><PERSON>())

		return
	}
	if keys[2] != "TemplateID" {
		t.<PERSON>("GetJSONK<PERSON>s failed" + err.<PERSON><PERSON><PERSON>())

		return
	}
	if keys[3] != "TemplateInfo" {
		t.<PERSON>("GetJSONKeys failed" + err.<PERSON><PERSON><PERSON>())

		return
	}
	if keys[4] != "Limit" {
		t.<PERSON><PERSON>("GetJ<PERSON><PERSON><PERSON><PERSON><PERSON> failed" + err.Error())

		return
	}

	fmt.Println(keys)
}
