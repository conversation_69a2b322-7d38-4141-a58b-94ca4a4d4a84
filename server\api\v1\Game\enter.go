package Game

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	GameApi
	GameRolePriceApi
	PricingRuleApi
	DeliveryrecordApi
	UserGameQuotaApi
}

var (
	gameService           = service.ServiceGroupApp.GameServiceGroup.GameService
	gameRolePriceService  = service.ServiceGroupApp.GameServiceGroup.GameRolePriceService
	pricingRuleService    = service.ServiceGroupApp.GameServiceGroup.PricingRuleService
	deliveryrecordService = service.ServiceGroupApp.GameServiceGroup.DeliveryrecordService
	userGameQuotaService  = service.ServiceGroupApp.GameServiceGroup.UserGameQuotaService
)
