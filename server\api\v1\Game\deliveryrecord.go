package Game

import (
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DeliveryrecordApi struct{}

// CreateDeliveryrecord 创建提取记录
// @Tags Deliveryrecord
// @Summary 创建提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Deliveryrecord true "创建提取记录"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /deliveryrecord/createDeliveryrecord [post]
func (deliveryrecordApi *DeliveryrecordApi) CreateDeliveryrecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var deliveryrecord Game.Deliveryrecord
	err := c.ShouldBindJSON(&deliveryrecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if utils.GetUserName(c) != "admin" {

	}
	err = deliveryrecordService.CreateDeliveryrecord(ctx, &deliveryrecord)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteDeliveryrecord 删除提取记录
// @Tags Deliveryrecord
// @Summary 删除提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Deliveryrecord true "删除提取记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /deliveryrecord/deleteDeliveryrecord [delete]
func (deliveryrecordApi *DeliveryrecordApi) DeleteDeliveryrecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := deliveryrecordService.DeleteDeliveryrecord(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteDeliveryrecordByIds 批量删除提取记录
// @Tags Deliveryrecord
// @Summary 批量删除提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /deliveryrecord/deleteDeliveryrecordByIds [delete]
func (deliveryrecordApi *DeliveryrecordApi) DeleteDeliveryrecordByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := deliveryrecordService.DeleteDeliveryrecordByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateDeliveryrecord 更新提取记录
// @Tags Deliveryrecord
// @Summary 更新提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.Deliveryrecord true "更新提取记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /deliveryrecord/updateDeliveryrecord [put]
func (deliveryrecordApi *DeliveryrecordApi) UpdateDeliveryrecord(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var deliveryrecord Game.Deliveryrecord
	err := c.ShouldBindJSON(&deliveryrecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = deliveryrecordService.UpdateDeliveryrecord(ctx, deliveryrecord)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindDeliveryrecord 用id查询提取记录
// @Tags Deliveryrecord
// @Summary 用id查询提取记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询提取记录"
// @Success 200 {object} response.Response{data=Game.Deliveryrecord,msg=string} "查询成功"
// @Router /deliveryrecord/findDeliveryrecord [get]
func (deliveryrecordApi *DeliveryrecordApi) FindDeliveryrecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	redeliveryrecord, err := deliveryrecordService.GetDeliveryrecord(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(redeliveryrecord, c)
}

// GetDeliveryrecordList 分页获取提取记录列表
// @Tags Deliveryrecord
// @Summary 分页获取提取记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.DeliveryrecordSearch true "分页获取提取记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /deliveryrecord/getDeliveryrecordList [get]
func (deliveryrecordApi *DeliveryrecordApi) GetDeliveryrecordList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo GameReq.DeliveryrecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	user := utils.GetUserName(c)
	UserAuthorityId := utils.GetUserAuthorityId(c)
	fmt.Println("UserAuthorityId", UserAuthorityId, user)
	if UserAuthorityId != 888 {
		// 非管理员只能查看自己的提取记录
		pageInfo.User = &user
	} else {
		pageInfo.User = nil
	}
	list, total, err := deliveryrecordService.GetDeliveryrecordInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetDeliveryrecordPublic 不需要鉴权的提取记录接口
// @Tags Deliveryrecord
// @Summary 不需要鉴权的提取记录接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deliveryrecord/getDeliveryrecordPublic [get]
func (deliveryrecordApi *DeliveryrecordApi) GetDeliveryrecordPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	deliveryrecordService.GetDeliveryrecordPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的提取记录接口信息",
	}, "获取成功", c)
}

// FindAccount
func (deliveryrecordApi *DeliveryrecordApi) FindAccount(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var accountReq GameReq.FindAccountReq
	err := c.ShouldBindQuery(&accountReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	db := global.GVA_DB.Table("data_news_user")
	if accountReq.RoleNum != nil && *accountReq.RoleNum != "" {
		db = db.Where("code = ?", *accountReq.RoleNum)
	} else {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:账号编号不能为空", c)
		return
	}
	var accounts map[string]any
	err = db.Find(&accounts).Limit(1).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	if accounts == nil || len(accounts) == 0 {
		response.FailWithMessage("获取失败:账号不存在", c)
		return
	}

	if accounts["state"] != nil && accounts["state"].(int32) == 1 {
		response.FailWithMessage("获取失败:账号已被提取", c)
		return
	}
	//去掉 username pass
	delete(accounts, "username")
	delete(accounts, "pass")
	resp, err := pricingRuleService.CalePricing(ctx, GameReq.CalePricingReq{
		GameId:     int(accounts["item_id"].(int32)),
		RoleCodes:  accounts["user_role"].(string),
		ServerCode: accounts["district"].(string),
	})
	if err != nil {
		global.GVA_LOG.Error("计算失败!", zap.Error(err))
		response.FailWithMessage("计算失败:"+err.Error(), c)
		return
	}
	accounts["price"] = strconv.Itoa(int(resp.Price))
	accounts["logText"] = resp.LogText
	response.OkWithData(accounts, c)
}

// PickAccount
func (deliveryrecordApi *DeliveryrecordApi) PickAccount(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var accountReq GameReq.PickAccountReq
	err := c.ShouldBindQuery(&accountReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if accountReq.Price == nil || *accountReq.Price <= 0 {
		response.FailWithMessage("获取失败:价格不能为空", c)
		return
	}
	//判断用户账号是否被禁用
	userName := utils.GetUserName(c)
	var user Game.UserGameQuota
	err = global.GVA_DB.Model(&Game.UserGameQuota{}).Where("username = ?", userName).First(&user).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	if *user.Status == 0 {
		response.FailWithMessage("获取失败:账号已被禁用", c)
		return
	}

	db := global.GVA_DB.Table("data_news_user")
	if accountReq.RoleNum != nil && *accountReq.RoleNum != "" {
		db = db.Where("code = ?", *accountReq.RoleNum)
	} else {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:账号编号不能为空", c)
		return
	}
	var accounts map[string]any
	err = db.Find(&accounts).Limit(1).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	if accounts["state"].(int32) == 1 {
		response.FailWithMessage("获取失败:账号已被提取", c)
		return
	}

	//DONE 校验价格是否正确,防止被修改
	gameId := int(accounts["item_id"].(int32))

	RoleCodes := accounts["user_role"].(string)
	resp, err := pricingRuleService.CalePricing(ctx, GameReq.CalePricingReq{
		GameId:     gameId,
		RoleCodes:  RoleCodes,
		ServerCode: accounts["district"].(string),
	})

	if err != nil {
		global.GVA_LOG.Error("计算失败!", zap.Error(err))
		response.FailWithMessage("计算失败:"+err.Error(), c)
		return
	}
	priceStr := strconv.Itoa(int(resp.Price))
	reqPriceStr := strconv.Itoa(*accountReq.Price)
	if priceStr != reqPriceStr {
		response.FailWithMessage("获取失败:价格错误", c)
		return
	}
	//done 获取当前用户的总额度是否足够
	var userGameQuota Game.UserGameQuota
	err = global.GVA_DB.Model(&Game.UserGameQuota{}).Where("username = ?", userName).First(&userGameQuota).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	if userGameQuota.TotalQuota == nil {
		response.FailWithMessage("获取失败:总额度不能为空", c)
		return
	}
	if userGameQuota.UsedQuota == nil {
		userGameQuota.UsedQuota = new(int)
		*userGameQuota.UsedQuota = 0
	}
	if *userGameQuota.TotalQuota-*userGameQuota.UsedQuota-*accountReq.Price < 0 {
		response.FailWithMessage("获取失败:总额度不足", c)
		return
	}

	//修改 tiqu_user_id,tiqu_username,tiqu_text,status

	err = db.Updates(map[string]interface{}{
		//"tiqu_user_id":  userName,
		"tiqu_username": userName,
		//"tiqu_text":     accounts["pass"],
		"state": 1,
	}).Limit(1).Error
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败:"+err.Error(), c)
		return
	}

	//done  返回 accountText
	splitStr := "----"
	accountText := ""
	accountText = accounts["code"].(string) + splitStr + accounts["item_name"].(string) + splitStr + accounts["item_name"].(string) + splitStr + accounts["username"].(string)
	accountText += splitStr + accounts["pass"].(string) + splitStr + accounts["mail"].(string) + splitStr + accounts["mail_pass"].(string)
	accountText += splitStr + accounts["district"].(string) + splitStr + accounts["user_role"].(string) + splitStr + accounts["mail_pass"].(string)
	accountText += splitStr + accounts["ascription_name"].(string) + splitStr + accounts["text"].(string)

	var GameId = int(accounts["item_id"].(int32))
	Code := accounts["code"].(string)
	Price := int(*accountReq.Price)
	PickDate := time.Now()
	//写入提取记录
	deliveryrecord := Game.Deliveryrecord{
		GameId:   &GameId,
		Code:     &Code,
		Price:    &Price,
		User:     &userName,
		PickDate: &PickDate,
		Info:     &accountText,
	}
	err = deliveryrecordService.CreateDeliveryrecord(ctx, &deliveryrecord)
	if err != nil {
		global.GVA_LOG.Error("写入提取记录失败!", zap.Error(err))
		response.FailWithMessage("写入提取记录失败:"+err.Error(), c)
		return
	}
	//done 叠加额度
	if userGameQuota.UsedQuota == nil {
		userGameQuota.UsedQuota = new(int)
		*userGameQuota.UsedQuota = 0
	}
	err = global.GVA_DB.Model(&Game.UserGameQuota{}).Where("username = ?", userName).Update("used_quota", *userGameQuota.UsedQuota+Price).Error
	if err != nil {
		global.GVA_LOG.Error("更新已用额度失败!", zap.Error(err))
		response.FailWithMessage("更新已用额度失败:"+err.Error(), c)
		return
	}

	accounts["accountText"] = accountText

	response.OkWithData(accounts, c)
}

//需要 获取今日效率 本月效率 使用额度/总额度
