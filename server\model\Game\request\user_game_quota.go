
package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	
)

type UserGameQuotaSearch struct{
    Username  *string `json:"username" form:"username" `
    GameId  *int `json:"gameId" form:"gameId" `
    TotalQuota  *int `json:"totalQuota" form:"totalQuota" `
    UsedQuota  *int `json:"usedQuota" form:"usedQuota" `
    DailyLimit  *int `json:"dailyLimit" form:"dailyLimit" `
    request.PageInfo
    Sort  string `json:"sort" form:"sort"`
    Order string `json:"order" form:"order"`
}
