package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type UserGameQuotaSearch struct {
	Username   *string `json:"username" form:"username" `
	GameId     *int    `json:"gameId" form:"gameId" `
	TotalQuota *int    `json:"totalQuota" form:"totalQuota" `
	UsedQuota  *int    `json:"usedQuota" form:"usedQuota" `
	DailyLimit *int    `json:"dailyLimit" form:"dailyLimit" `
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

// UserGameQuotaStatistics 用户游戏额度统计响应结构体
type UserGameQuotaStatistics struct {
	TodaySales     int `json:"todaySales"`     // 今日销量(记录数量)
	TodayUsedQuota int `json:"todayUsedQuota"` // 今日使用额度
	MonthSales     int `json:"monthSales"`     // 月销量
	MonthUsedQuota int `json:"monthUsedQuota"` // 月使用额度
}
