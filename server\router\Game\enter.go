package Game

import api "github.com/flipped-aurora/gin-vue-admin/server/api/v1"

type RouterGroup struct {
	GameRouter
	GameRolePriceRouter
	PricingRuleRouter
	DeliveryrecordRouter
	UserGameQuotaRouter
}

var (
	gameApi           = api.ApiGroupApp.GameApiGroup.GameApi
	gameRolePriceApi  = api.ApiGroupApp.GameApiGroup.GameRolePriceApi
	pricingRuleApi    = api.ApiGroupApp.GameApiGroup.PricingRuleApi
	deliveryrecordApi = api.ApiGroupApp.GameApiGroup.DeliveryrecordApi
	userGameQuotaApi  = api.ApiGroupApp.GameApiGroup.UserGameQuotaApi
)
