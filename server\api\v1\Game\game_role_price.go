package Game

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type GameRolePriceApi struct{}

// CreateGameRolePrice 创建gameRolePrice表
// @Tags GameRolePrice
// @Summary 创建gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.GameRolePrice true "创建gameRolePrice表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /gameRolePrice/createGameRolePrice [post]
func (gameRolePriceApi *GameRolePriceApi) CreateGameRolePrice(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var gameRolePrice Game.GameRolePrice
	err := c.ShouldBindJSON(&gameRolePrice)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = gameRolePriceService.CreateGameRolePrice(ctx, &gameRolePrice)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteGameRolePrice 删除gameRolePrice表
// @Tags GameRolePrice
// @Summary 删除gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.GameRolePrice true "删除gameRolePrice表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /gameRolePrice/deleteGameRolePrice [delete]
func (gameRolePriceApi *GameRolePriceApi) DeleteGameRolePrice(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := gameRolePriceService.DeleteGameRolePrice(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteGameRolePriceByIds 批量删除gameRolePrice表
// @Tags GameRolePrice
// @Summary 批量删除gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /gameRolePrice/deleteGameRolePriceByIds [delete]
func (gameRolePriceApi *GameRolePriceApi) DeleteGameRolePriceByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := gameRolePriceService.DeleteGameRolePriceByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateGameRolePrice 更新gameRolePrice表
// @Tags GameRolePrice
// @Summary 更新gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body Game.GameRolePrice true "更新gameRolePrice表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /gameRolePrice/updateGameRolePrice [put]
func (gameRolePriceApi *GameRolePriceApi) UpdateGameRolePrice(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var gameRolePrice Game.GameRolePrice
	err := c.ShouldBindJSON(&gameRolePrice)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = gameRolePriceService.UpdateGameRolePrice(ctx, gameRolePrice)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindGameRolePrice 用id查询gameRolePrice表
// @Tags GameRolePrice
// @Summary 用id查询gameRolePrice表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询gameRolePrice表"
// @Success 200 {object} response.Response{data=Game.GameRolePrice,msg=string} "查询成功"
// @Router /gameRolePrice/findGameRolePrice [get]
func (gameRolePriceApi *GameRolePriceApi) FindGameRolePrice(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	regameRolePrice, err := gameRolePriceService.GetGameRolePrice(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(regameRolePrice, c)
}

// GetGameRolePriceList 分页获取gameRolePrice表列表
// @Tags GameRolePrice
// @Summary 分页获取gameRolePrice表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query GameReq.GameRolePriceSearch true "分页获取gameRolePrice表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /gameRolePrice/getGameRolePriceList [get]
func (gameRolePriceApi *GameRolePriceApi) GetGameRolePriceList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo GameReq.GameRolePriceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := gameRolePriceService.GetGameRolePriceInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetGameRolePricePublic 不需要鉴权的gameRolePrice表接口
// @Tags GameRolePrice
// @Summary 不需要鉴权的gameRolePrice表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /gameRolePrice/getGameRolePricePublic [get]
func (gameRolePriceApi *GameRolePriceApi) GetGameRolePricePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	gameRolePriceService.GetGameRolePricePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的gameRolePrice表接口信息",
	}, "获取成功", c)
}

// SyncGameRolePrice
func (gameRolePriceApi *GameRolePriceApi) SyncGameRolePrice(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()
	gameId := c.Query("gameId")
	gameIdInt, err := strconv.Atoi(gameId)
	if err != nil {
		global.GVA_LOG.Error("同步失败!", zap.Error(err))
		response.FailWithMessage("同步失败:"+err.Error(), c)
		return
	}

	err = gameRolePriceService.SyncGameRolePrice(ctx, gameIdInt)
	if err != nil {
		global.GVA_LOG.Error("同步失败!", zap.Error(err))
		response.FailWithMessage("同步失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("同步成功", c)
}

// BatchUpdatePrice 批量更新价格
// @Tags GameRolePrice
// @Summary 批量更新gameRolePrice表价格
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body GameReq.BatchUpdatePriceReq true "批量更新价格参数"
// @Success 200 {object} response.Response{msg=string} "批量更新成功"
// @Router /gameRolePrice/batchUpdatePrice [put]
func (gameRolePriceApi *GameRolePriceApi) BatchUpdatePrice(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req GameReq.BatchUpdatePriceReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = gameRolePriceService.BatchUpdatePrice(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("批量更新价格失败!", zap.Error(err))
		response.FailWithMessage("批量更新价格失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量更新价格成功", c)
}
