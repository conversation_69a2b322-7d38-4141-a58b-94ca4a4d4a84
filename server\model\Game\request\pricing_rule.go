package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type PricingRuleSearch struct {
	Status        *bool    `json:"status" form:"status" `
	RoleCodes     *string  `json:"roleCodes" form:"roleCodes" `
	RoleBasePrice *float64 `json:"roleBasePrice" form:"roleBasePrice" `
	GameId        *int     `json:"gameId" form:"gameId" `
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

type CalePricingReq struct {
	GameId     int    `json:"gameId" form:"gameId" `
	RoleCodes  string `json:"roleCodes" form:"roleCodes" `   // 角色代码列表，逗号分隔
	ServerCode string `json:"serverCode" form:"serverCode" ` // 服务器代码
}

type CalePricingRes struct {
	RuleNum int     `json:"ruleNum" form:"ruleNum" ` // 规则编号
	Price   float64 `json:"price" form:"price" `
	LogText string  `json:"logText" form:"logText" `
}
