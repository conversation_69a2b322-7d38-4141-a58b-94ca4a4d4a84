package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type PricingRuleSearch struct {
	Status        *bool    `json:"status" form:"status" `
	RoleCodes     *string  `json:"roleCodes" form:"roleCodes" `
	RoleBasePrice *float64 `json:"roleBasePrice" form:"roleBasePrice" `
	GameId        *int     `json:"gameId" form:"gameId" `
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

type CalePricingReq struct {
	GameId     int    `json:"gameId" form:"gameId" `
	RoleCodes  string `json:"roleCodes" form:"roleCodes" `   // 角色代码列表，逗号分隔
	ServerCode string `json:"serverCode" form:"serverCode" ` // 服务器代码
}

type CalePricingRes struct {
	RuleNum int     `json:"ruleNum" form:"ruleNum" ` // 规则编号
	Price   float64 `json:"price" form:"price" `
	LogText string  `json:"logText" form:"logText" `
}

// BatchUpdatePricingRuleReq 批量更新定价规则请求结构体
type BatchUpdatePricingRuleReq struct {
	Ids                   []int    `json:"ids" binding:"required"`          // 要更新的记录ID列表
	ServerCode            *string  `json:"serverCode,omitempty"`            // 指定服务器代码
	ServerPremium         *float64 `json:"serverPremium,omitempty"`         // 服务器溢价百分比
	UnspecifiedMultiplier *float64 `json:"unspecifiedMultiplier,omitempty"` // 未指定角色定价倍率
	QuantityCondition     *string  `json:"quantityCondition,omitempty"`     // 数量条件
	DiscountRate          *float64 `json:"discountRate,omitempty"`          // 整体折扣率
	Repeats               *string  `json:"repeats,omitempty"`               // 重复价格设置
}
