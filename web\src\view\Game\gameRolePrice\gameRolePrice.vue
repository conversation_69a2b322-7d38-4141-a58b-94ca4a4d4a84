
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
          <el-form-item label="所属游戏" prop="gameId">
              <el-select v-model="searchInfo.gameId" placeholder="请选择游戏" @change="handleGameChange" clearable>
                <el-option 
                  v-for="item in gameOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="角色名称" prop="roleName">
  <el-input v-model="searchInfo.roleName" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="角色代码" prop="roleCode">
  <el-input v-model="searchInfo.roleCode" placeholder="搜索条件" />
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  type="primary" icon="plus" @click="syncGameRolePriceList()">同步游戏角色</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            <el-button  type="warning" icon="edit" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="openBatchUpdateDialog">批量修改价格</el-button>
            <ExportTemplate  template-id="Game_GameRolePrice" />
            <ExportExcel  template-id="Game_GameRolePrice" filterDeleted/>
            <ImportExcel  template-id="Game_GameRolePrice" @on-success="getTableData" />
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="id字段" prop="id" width="120" />

            <!-- <el-table-column align="left" label="所属游戏id" prop="gameId" width="120" /> -->

            <!-- <el-table-column align="left" label="角色ID（关联data_news_item_role的id）" prop="roleId" width="120" /> -->

            <el-table-column align="left" label="角色名称" prop="roleName" width="120" />

            <el-table-column align="left" label="角色代码" prop="roleCode" width="120" />

            <el-table-column align="left" label="基础价格" prop="basePrice" width="120" />

            <el-table-column align="left" label="创建时间" prop="createAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="更新时间" prop="updateAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updateAt) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateGameRolePriceFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="所属游戏id:" prop="gameId">
    <el-input v-model.number="formData.gameId" :clearable="true" placeholder="请输入所属游戏id" />
</el-form-item>
            <!-- <el-form-item label="角色ID（关联data_news_item_role的id）:" prop="roleId">
    <el-input v-model.number="formData.roleId" :clearable="true" placeholder="请输入角色ID（关联data_news_item_role的id）" />
</el-form-item> -->
            <el-form-item label="角色名称:" prop="roleName">
    <el-input v-model="formData.roleName" :clearable="true" placeholder="请输入角色名称" />
</el-form-item>
            <el-form-item label="角色代码:" prop="roleCode">
    <el-input v-model="formData.roleCode" :clearable="true" placeholder="请输入角色代码" />
</el-form-item>
            <el-form-item label="基础价格:" prop="basePrice">
    <el-input-number v-model="formData.basePrice" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="id字段">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="所属游戏id">
    {{ detailFrom.gameId }}
</el-descriptions-item> -->
                    <!-- <el-descriptions-item label="角色ID（关联data_news_item_role的id）">
    {{ detailFrom.roleId }}
</el-descriptions-item> -->
                    <el-descriptions-item label="角色名称">
    {{ detailFrom.roleName }}
</el-descriptions-item>
                    <el-descriptions-item label="角色代码">
    {{ detailFrom.roleCode }}
</el-descriptions-item>
                    <el-descriptions-item label="基础价格">
    {{ detailFrom.basePrice }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.createAt }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updateAt }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

    <!-- 批量修改价格弹窗 -->
    <el-dialog v-model="batchUpdateVisible" title="批量修改价格" width="500px" :before-close="closeBatchUpdateDialog">
      <el-form :model="batchUpdateForm" ref="batchUpdateFormRef" :rules="batchUpdateRules" label-width="120px">
        <el-form-item label="修改类型" prop="updateType">
          <el-radio-group v-model="batchUpdateForm.updateType">
            <el-radio value="fixed">设置固定值</el-radio>
            <el-radio value="percentage">按百分比调整</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          :label="batchUpdateForm.updateType === 'fixed' ? '固定价格' : '百分比值'"
          prop="value">
          <el-input-number
            v-model="batchUpdateForm.value"
            :precision="2"
            :min="0.01"
            style="width: 100%"
            :placeholder="batchUpdateForm.updateType === 'fixed' ? '请输入固定价格' : '请输入百分比(如200表示2倍，50表示5折)'"
          />
          <div class="form-tip">
            <span v-if="batchUpdateForm.updateType === 'fixed'">
              将选中记录的价格设置为固定值
            </span>
            <span v-else>
              按百分比调整现有价格：200表示2倍，50表示5折
            </span>
          </div>
        </el-form-item>

        <el-form-item label="选中记录">
          <el-tag type="info">已选择 {{ multipleSelection.length }} 条记录</el-tag>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeBatchUpdateDialog">取消</el-button>
          <el-button type="primary" :loading="batchUpdateLoading" @click="confirmBatchUpdate">确定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import {
  createGameRolePrice,
  deleteGameRolePrice,
  deleteGameRolePriceByIds,
  updateGameRolePrice,
  findGameRolePrice,
  getGameRolePriceList,
  syncGameRolePrice,
  batchUpdatePrice
} from '@/api/Game/gameRolePrice'
// 导入获取游戏列表的API
import { getGameList } from '@/api/Game/game'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, watch } from 'vue'
import { useAppStore } from "@/pinia"

// 导出组件
import ExportExcel from '@/components/exportExcel/exportExcel.vue'
// 导入组件
import ImportExcel from '@/components/exportExcel/importExcel.vue'
// 导出模板组件
import ExportTemplate from '@/components/exportExcel/exportTemplate.vue'

defineOptions({
    name: 'GameRolePrice'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

 

// 游戏选项列表
const gameOptions = ref([])

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            gameId: undefined,
            roleId: undefined,
            roleName: '',
            roleCode: '',
            basePrice: 0,
        })

// 获取游戏列表并设置默认游戏
const getGameOptions = async () => {
  try {
    const res = await getGameList({ page: 1, pageSize: 1000, isEnable: 1 }) // 获取启用的游戏
    if (res.code === 0 && res.data.list && res.data.list.length > 0) {
      gameOptions.value = res.data.list
      //设置默认游戏
      if(res.data.list.length > 0) {
        formData.value.gameId = res.data.list[0].id
        searchInfo.value.gameId = res.data.list[0].id
      }
       

      // 获取表格数据
      getTableData()
    }
  } catch (error) {
    console.error('获取游戏列表失败:', error)
  }
}

// 处理游戏选择变化
const handleGameChange = (val) => {
  searchInfo.value.gameId = val
 
  // 重新获取表格数据
  getTableData()
}

 
// 在组件挂载时获取游戏列表
onMounted(() => {
  getGameOptions()
})

// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getGameRolePriceList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize

  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteGameRolePriceFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteGameRolePriceByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateGameRolePriceFunc = async(row) => {
    const res = await findGameRolePrice({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteGameRolePriceFunc = async (row) => {
    const res = await deleteGameRolePrice({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 同步游戏角色
const syncGameRolePriceList = async () => {
     const res = await syncGameRolePrice({gameId: searchInfo.value.gameId})
    if (res.code === 0) {
        ElMessage({
            type: 'success',
            message: '同步成功'
        })
        getTableData()
    }
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        gameId: undefined,
        roleId: undefined,
        roleName: '',
        roleCode: '',
        basePrice: 0,
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createGameRolePrice(formData.value)
                  break
                case 'update':
                  res = await updateGameRolePrice(formData.value)
                  break
                default:
                  res = await createGameRolePrice(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)

// 批量修改相关数据
const batchUpdateVisible = ref(false)
const batchUpdateLoading = ref(false)
const batchUpdateFormRef = ref()
const batchUpdateForm = ref({
  updateType: 'fixed', // fixed: 固定值, percentage: 百分比
  value: 0
})

// 批量修改表单验证规则
const batchUpdateRules = reactive({
  updateType: [
    { required: true, message: '请选择修改类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入修改值', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '修改值必须大于0', trigger: 'blur' }
  ]
})


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findGameRolePrice({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 打开批量修改弹窗
const openBatchUpdateDialog = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage({
      type: 'warning',
      message: '请选择要修改的记录'
    })
    return
  }

  // 重置表单
  batchUpdateForm.value = {
    updateType: 'fixed',
    value: 0
  }

  batchUpdateVisible.value = true
}

// 关闭批量修改弹窗
const closeBatchUpdateDialog = () => {
  batchUpdateVisible.value = false
  batchUpdateForm.value = {
    updateType: 'fixed',
    value: 0
  }
  // 清除表单验证
  batchUpdateFormRef.value?.clearValidate()
}

// 确认批量修改
const confirmBatchUpdate = async () => {
  // 表单验证
  const valid = await batchUpdateFormRef.value?.validate()
  if (!valid) return

  // 确认对话框
  const updateTypeText = batchUpdateForm.value.updateType === 'fixed' ? '固定值' : '百分比'
  const valueText = batchUpdateForm.value.updateType === 'fixed'
    ? `${batchUpdateForm.value.value}元`
    : `${batchUpdateForm.value.value}%`

  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${multipleSelection.value.length} 条记录的价格按${updateTypeText}修改为 ${valueText} 吗？`,
      '确认批量修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchUpdateLoading.value = true

    // 提取选中记录的ID
    const ids = multipleSelection.value.map(item => item.id)

    // 调用批量更新API
    const res = await batchUpdatePrice({
      ids: ids,
      updateType: batchUpdateForm.value.updateType,
      value: batchUpdateForm.value.value
    })

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '批量修改成功'
      })

      // 关闭弹窗并刷新数据
      closeBatchUpdateDialog()
      getTableData()

      // 清空选择
      multipleSelection.value = []
    }
  } catch (error) {
    // 用户取消或其他错误
    console.log('批量修改取消或失败:', error)
  } finally {
    batchUpdateLoading.value = false
  }
}


</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

.el-tag {
  margin-right: 8px;
}
</style>


