package Game

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/Game"
	GameReq "github.com/flipped-aurora/gin-vue-admin/server/model/Game/request"
	stringEx2 "github.com/littleboss01/MyUitls/stringEx"
)

type PricingRuleService struct{}

// CreatePricingRule 创建pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) CreatePricingRule(ctx context.Context, pricingRule *Game.PricingRule) (err error) {
	err = global.GVA_DB.Create(pricingRule).Error
	return err
}

// DeletePricingRule 删除pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) DeletePricingRule(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&Game.PricingRule{}, "id = ?", id).Error
	return err
}

// DeletePricingRuleByIds 批量删除pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) DeletePricingRuleByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]Game.PricingRule{}, "id in ?", ids).Error
	return err
}

// UpdatePricingRule 更新pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) UpdatePricingRule(ctx context.Context, pricingRule Game.PricingRule) (err error) {
	err = global.GVA_DB.Model(&Game.PricingRule{}).Where("id = ?", pricingRule.Id).Updates(&pricingRule).Error
	return err
}

// GetPricingRule 根据id获取pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) GetPricingRule(ctx context.Context, id string) (pricingRule Game.PricingRule, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&pricingRule).Error
	return
}

// GetPricingRuleInfoList 分页获取pricingRule表记录
// Author [yourname](https://github.com/yourname)
func (pricingRuleService *PricingRuleService) GetPricingRuleInfoList(ctx context.Context, info GameReq.PricingRuleSearch) (list []Game.PricingRule, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&Game.PricingRule{})
	var pricingRules []Game.PricingRule
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	if info.RoleCodes != nil && *info.RoleCodes != "" {
		db = db.Where("role_codes = ?", *info.RoleCodes)
	}
	if info.RoleBasePrice != nil {
		db = db.Where("role_base_price = ?", *info.RoleBasePrice)
	}
	if info.GameId != nil {
		db = db.Where("game_id = ?", *info.GameId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["role_base_price"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&pricingRules).Error
	return pricingRules, total, err
}
func (pricingRuleService *PricingRuleService) GetPricingRulePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// calePricing 计算价格
func (pricingRuleService *PricingRuleService) CalePricing(ctx context.Context, req GameReq.CalePricingReq) (res GameReq.CalePricingRes, err error) {
	// 1.根据game_id   查询pricingRule表
	var pricingRules []Game.PricingRule
	db := global.GVA_DB.Model(&Game.PricingRule{})
	err = db.Where("game_id = ? ", req.GameId).Find(&pricingRules).Error
	if err != nil {
		return res, err
	}
	//取出未定义角色的基础价格
	db2 := global.GVA_DB.Model(&Game.GameRolePrice{})
	var gameRolePrices []Game.GameRolePrice
	err = db2.Where("game_id = ? ", req.GameId).Find(&gameRolePrices).Error
	if err != nil {
		return res, err
	}
	//[]Game.GameRolePrice 转换为map
	var gameRolePriceMap = make(map[string]float64)
	if len(gameRolePrices) == 0 {
		res.LogText += " gameRolePrices长度为 0"
	} else {
		for _, gameRolePrice := range gameRolePrices {
			//log.Println(*gameRolePrice.RoleCode, *gameRolePrice.BasePrice)
			if gameRolePrice.BasePrice == nil || gameRolePrice.RoleCode == nil {
				continue
			}
			gameRolePriceMap[*gameRolePrice.RoleCode] = *gameRolePrice.BasePrice
		}

	}
	// 2.遍历pricingRules，计算价格,找到符合条件 且 价格最高的
	if len(pricingRules) == 0 {
		res.LogText += " pricingRules长度为 0"
	} else {
		var maxPrice float64 = 0
		for _, pricingRule := range pricingRules {
			// 2.1. 计算价格

			ruleNum, price, logText := calePricingByPricingRule(ctx, pricingRule, gameRolePriceMap, req.RoleCodes, req.ServerCode)
			log.Println("ruleNum, price, logText", ruleNum, price, logText)
			// 2.4.判断是否是最高价格
			if price > maxPrice {
				maxPrice = price
				res.RuleNum = ruleNum
				res.Price = price
				res.LogText = logText
			}
		}
	}

	// 3.返回结果
	if res.Price == 0 {
		return res, errors.New("未找到符合条件的价格" + res.LogText)
	} else {
		//个位数四舍五入
		res.Price = math.Round(res.Price/10) * 10
	}

	return res, nil
}

// 计算Game.PricingRule的价格
func calePricingByPricingRule(ctx context.Context, pricingRule Game.PricingRule, gameRolePriceMap map[string]float64, roleCodes, serverCode string) (rulenubm int, price float64, logText string) {
	caleStr := ""
	//拆分roleCodes
	roleCodeArr := strings.Split(roleCodes, ",")
	roloCodeArr_server := strings.Split(*pricingRule.RoleCodes, ",")
	//判断roleCodeArr是否符合RoleCodes

	//common, unspecifiedArr, _ := analyzeArrays(roleCodeArr, roloCodeArr_server)
	//common, unspecifiedArr := checkArrayInclusion(roleCodeArr, roloCodeArr_server)
	// log.Println("roleCodes%v  roloCodeArr_server%v common%v unspecifiedArr%v", roleCodes, roloCodeArr_server, common, unspecifiedArr)
	// if len(roloCodeArr_server) != len(common) {
	// 	price = 0
	// } else {
	// 	price = *pricingRule.RoleBasePrice
	// }
	isHave, unspecifiedArr := stringEx2.CheckArrayContains(roloCodeArr_server, roleCodeArr)
	if !isHave {
		price = 0
	} else {
		price = *pricingRule.RoleBasePrice
	}

	logText += fmt.Sprintf("基础价格%v", price)
	caleStr += fmt.Sprintf("%v", *pricingRule.RoleBasePrice)

	if pricingRule.QuantityCondition == nil || *pricingRule.QuantityCondition == "" {
		QuantityCondition := "0-0"
		pricingRule.QuantityCondition = &QuantityCondition
	}
	min, max, err := parseNumberRange(*pricingRule.QuantityCondition)
	if err == nil {
		var unspecifiedCount = len(unspecifiedArr)
		//if len(common) != len(roloCodeArr_server) { //在不满足条件的情况下使用基础价格叠加
		if !isHave {
			for _, roleCode := range roleCodeArr {
				if gameRolePriceMap[roleCode] == 0 {
					//price = price + 10
				} else {
					price = price + gameRolePriceMap[roleCode]
				}
				logText += fmt.Sprintf("+未指定角色:%v价格:%v=%v,", roleCode, gameRolePriceMap[roleCode], price)
				caleStr += fmt.Sprintf("+%v=%v,", gameRolePriceMap[roleCode], price)
			}
		} else {
			//判断未指定角色的数量是否在范围内
			if unspecifiedCount >= min && unspecifiedCount <= max {
				//计算未指定角色的价格
				for _, roleCode := range unspecifiedArr {
					if *pricingRule.UnspecifiedMultiplier > 0 { //防止倍率为0
						price = price + gameRolePriceMap[roleCode]**pricingRule.UnspecifiedMultiplier/100
					} else {
						price = price + gameRolePriceMap[roleCode]
					}
					logText += fmt.Sprintf("+未指定角色:%v价格:%v*倍率%v=%v,", roleCode, gameRolePriceMap[roleCode], *pricingRule.UnspecifiedMultiplier, price)
					caleStr += fmt.Sprintf("+%v*%v=%v,", gameRolePriceMap[roleCode], *pricingRule.UnspecifiedMultiplier, price)
				}
			} else {
				for _, roleCode := range unspecifiedArr {
					if gameRolePriceMap[roleCode] == 0 {
						//price = price + 10
					} else {
						price = price + gameRolePriceMap[roleCode]
					}
					logText += fmt.Sprintf("+未指定角色:%v价格:%v=%v,", roleCode, gameRolePriceMap[roleCode], price)
					caleStr += fmt.Sprintf("+%v=%v,", gameRolePriceMap[roleCode], price)
				}
			}
		}

	}

	if isHave {
		if pricingRule.Repeats != nil && *pricingRule.Repeats != "" {
			late := CalcLate(roloCodeArr_server)
			//[{"quantity":1,"price":2},{"quantity":600,"price":800}]
			//Repeats解析到json
			var repeats []map[string]int
			err := json.Unmarshal([]byte(*pricingRule.Repeats), &repeats)
			if err != nil {
				log.Println("Repeats解析失败", err)
			}
			//判断late是否在quantity中
			for _, repeat := range repeats {
				if repeat["quantity"] == late {
					price += float64(repeat["price"])
					logText += fmt.Sprintf("+重复:%v=%v,", repeat["quantity"], repeat["price"])
					caleStr += fmt.Sprintf("+%v=%v,", repeat["price"], price)
				}
			}
		}
	}

	if *pricingRule.DiscountRate > 0 && pricingRule.DiscountRate != nil {
		price = price * (*pricingRule.DiscountRate / 100)
		logText += fmt.Sprintf("折扣率:%v=%v/100,", *pricingRule.DiscountRate, price)
		caleStr += fmt.Sprintf("*%v=%v,", *pricingRule.DiscountRate/100, price)
	}

	if isHave {
		//卡片不匹配不计算 服务器倍率
		if serverCode == *pricingRule.ServerCode || (serverCode != "" && strings.Contains(*pricingRule.ServerCode, serverCode)) {
			if *pricingRule.ServerPremium != 0 {
				price = price * (*pricingRule.ServerPremium / 100)
				logText += fmt.Sprintf("*服务器溢价:%v=%v/100,", *pricingRule.ServerPremium, price)
				caleStr += fmt.Sprintf("*%v=%v,", *pricingRule.ServerPremium/100, price)
			}
		}
	}

	return *pricingRule.Id, price, logText + "\n" + caleStr
}

// 统计数组中每个元素的出现次数
func countElements(arr []string) map[string]int {
	counts := make(map[string]int)
	for _, item := range arr {
		counts[item]++
	}
	return counts
}

// 判断arr2是否包含arr1（考虑重复次数）
func checkArrayInclusion(arr1, arr2 []string) (common, missing []string) {
	// 统计两个数组中元素的出现次数
	counts1 := countElements(arr1)
	counts2 := countElements(arr2)

	// 计算相同元素（取两者中的最小次数）
	for item, count1 := range counts1 {
		if count2, exists := counts2[item]; exists {
			minCount := count1
			if count2 < minCount {
				minCount = count2
			}
			// 将相同元素按最小次数添加到common
			for i := 0; i < minCount; i++ {
				common = append(common, item)
			}
		} else {
			// arr2中完全不存在的元素
			for i := 0; i < count1; i++ {
				missing = append(missing, item)
			}
		}
	}
	return
}

func CalcLate(units []string) int {
	count := make(map[string]int)
	for _, unit := range units {
		count[unit]++
	}
	repeatCount := 0
	for _, v := range count {
		if v > 1 {
			repeatCount += v - 1
		}
	}
	return repeatCount
}

// 分析两个文本数组的重复和不重复部分
func analyzeArrays(arr1, arr2 []string) (common, onlyInArr1, onlyInArr2 []string) {
	// 统计第一个数组中每个元素的出现次数
	countMap1 := make(map[string]int)
	for _, item := range arr1 {
		countMap1[item]++
	}

	// 统计第二个数组中每个元素的出现次数
	countMap2 := make(map[string]int)
	for _, item := range arr2 {
		countMap2[item]++
	}

	// 找出共有的元素（交集）
	for item := range countMap1 {
		if _, exists := countMap2[item]; exists {
			common = append(common, item)
		}
	}

	// 找出只在第一个数组中出现的元素
	for item := range countMap1 {
		if _, exists := countMap2[item]; !exists {
			onlyInArr1 = append(onlyInArr1, item)
		}
	}

	// 找出只在第二个数组中出现的元素
	for item := range countMap2 {
		if _, exists := countMap1[item]; !exists {
			onlyInArr2 = append(onlyInArr2, item)
		}
	}

	return
}

// 解析3-3 3-5这样的数字范围
func parseNumberRange(rangeStr string) (min, max int, err error) {
	rangeArr := strings.Split(rangeStr, "-")
	if len(rangeArr) != 2 {
		return 0, 0, fmt.Errorf("invalid range format: %s", rangeStr)
	}
	min, err = strconv.Atoi(rangeArr[0])
	if err != nil {
		return 0, 0, err
	}
	max, err = strconv.Atoi(rangeArr[1])
	if err != nil {
		return 0, 0, err
	}
	return min, max, nil
}
