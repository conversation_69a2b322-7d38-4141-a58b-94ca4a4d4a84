package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type DeliveryrecordSearch struct {
	GameId *int    `json:"gameId" form:"gameId" `
	Code   *string `json:"code" form:"code" `
	Price  *int    `json:"price" form:"price" `
	User   *string `json:"user" form:"user" `
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`   // 排序字段，支持 id, pick_date 等
	Order string `json:"order" form:"order"` // 排序方向，ascending(升序) 或 descending(降序)
}

type FindAccountReq struct {
	RoleNum *string `json:"roleNum" form:"roleNum" `
}

type PickAccountReq struct {
	RoleNum *string `json:"roleNum" form:"roleNum" `
	Price   *int    `json:"price" form:"price" `
}
