
// 自动生成模板UserGameQuota
package Game
import (
	"time"
)

// userGameQuota表 结构体  UserGameQuota
type UserGameQuota struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;"size:20;`  //主键ID
  CreatedAt  *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`  //创建时间
  UpdatedAt  *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`  //更新时间
  DeletedAt  *time.Time `json:"deletedAt" form:"deletedAt" gorm:"comment:删除时间;column:deleted_at;"`  //删除时间
  UserUuid  *string `json:"userUuid" form:"userUuid" gorm:"comment:用户UUID;column:user_uuid;"size:191;`  //用户UUID
  Username  *string `json:"username" form:"username" gorm:"comment:用户登录名;column:username;"size:191;`  //用户登录名
  NickName  *string `json:"nickName" form:"nickName" gorm:"comment:用户昵称;column:nick_name;"size:191;`  //用户昵称
  GameId  *int `json:"gameId" form:"gameId" gorm:"comment:游戏ID;column:game_id;"size:10;`  //游戏ID
  TotalQuota  *int `json:"totalQuota" form:"totalQuota" gorm:"comment:总额度;column:total_quota;"size:10;`  //总额度
  UsedQuota  *int `json:"usedQuota" form:"usedQuota" gorm:"comment:已使用额度;column:used_quota;"size:10;`  //已使用额度
  DailyLimit  *int `json:"dailyLimit" form:"dailyLimit" gorm:"comment:每日限额;column:daily_limit;"size:10;`  //每日限额
  TodayUsed  *int `json:"todayUsed" form:"todayUsed" gorm:"comment:今日已用;column:today_used;"size:10;`  //今日已用
  LastResetDate  *time.Time `json:"lastResetDate" form:"lastResetDate" gorm:"comment:上次重置日期;column:last_reset_date;"`  //上次重置日期
  ExpirationDate  *time.Time `json:"expirationDate" form:"expirationDate" gorm:"comment:额度过期时间;column:expiration_date;"`  //额度过期时间
  Status  *int `json:"status" form:"status" gorm:"comment:状态(1正常,0禁用);column:status;"size:10;`  //状态(1正常,0禁用)
  Remark  *string `json:"remark" form:"remark" gorm:"comment:备注;column:remark;"size:255;`  //备注
}


// TableName userGameQuota表 UserGameQuota自定义表名 user_game_quota
func (UserGameQuota) TableName() string {
    return "user_game_quota"
}





