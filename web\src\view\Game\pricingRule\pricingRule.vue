
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
            <el-form-item label="所属游戏" prop="gameId">
              <el-select v-model="searchInfo.gameId" placeholder="请选择游戏" @change="handleGameChange" clearable>
                <el-option 
                  v-for="item in gameOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="规则状态(1启用,0禁用)" prop="status">
              <el-select v-model="searchInfo.status" clearable placeholder="请选择">
                <el-option key="1" label="启用" value="1"></el-option>
                <el-option key="0" label="禁用" value="0"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="指定角色代码列表，逗号分隔" prop="roleCodes">
              <el-input v-model="searchInfo.roleCodes" placeholder="搜索条件" />
            </el-form-item>
            
            <el-form-item label="指定角色基础价" prop="roleBasePrice">
        <el-input v-model.number="searchInfo.roleBasePrice" placeholder="搜索条件" />
      </el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 增加一个输入文本测试计算价格 -->
     <div  >
      <el-input v-model="formData.roleCodes" placeholder="输入角色代码" />      
      <el-input v-model="formData.serverCode" placeholder="输入服务器代码" />
      <el-button type="primary" @click="calculatePrice">计算价格</el-button>
      <!-- 显示价格和计算方式 -->
     <div> 规则编号: {{ ruleNum }}</div>
     <div> 价格: {{ price }}</div>
      <div> 计算方式: {{ logText }}</div>
     </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="id字段" prop="id" width="120" />

            <!-- <el-table-column align="left" label="所属游戏ID" prop="gameId" width="120" /> -->

            <el-table-column align="left" label="规则状态(1启用,0禁用)" prop="status" width="120">
    <template #default="scope">{{ scope.row.status }}</template>
</el-table-column>
            <el-table-column align="left" label="指定角色代码列表，逗号分隔" prop="roleCodes" width="120" />

            <el-table-column sortable align="left" label="指定角色基础价" prop="roleBasePrice" width="120" />

            <el-table-column align="left" label="指定服务器代码" prop="serverCode" width="120" />

            <el-table-column align="left" label="服务器溢价百分比" prop="serverPremium" width="120" />

            <el-table-column align="left" label="未指定角色定价倍率" prop="unspecifiedMultiplier" width="120" />

            <el-table-column align="left" label="数量条件" prop="quantityCondition" width="120" />

            <el-table-column align="left" label="整体折扣率(10%-200%)" prop="discountRate" width="120" />

        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updatePricingRuleFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="所属游戏:" prop="gameId">
              <el-select v-model="formData.gameId" placeholder="请选择游戏" clearable>
                <el-option 
                  v-for="item in gameOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="指定角色代码列表，逗号分隔:" prop="roleCodes">
    <el-input v-model="formData.roleCodes" :clearable="true" :precision="2" placeholder="请输入指定角色代码列表，逗号分隔" />
</el-form-item>
            <el-form-item label="指定角色基础价:" prop="roleBasePrice">
    <el-input-number v-model="formData.roleBasePrice" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="指定服务器代码:" prop="serverCode">
    <el-input v-model="formData.serverCode" :clearable="true" placeholder="请输入指定服务器代码" />
</el-form-item>
            <el-form-item label="服务器溢价百分比:" prop="serverPremium">
    <el-input-number v-model="formData.serverPremium" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="未指定角色定价倍率:" prop="unspecifiedMultiplier">
    <el-input-number v-model="formData.unspecifiedMultiplier" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="数量条件:" prop="quantityCondition">
    <el-input v-model="formData.quantityCondition" :clearable="true" placeholder="请输入数量条件" />
</el-form-item>
            <el-form-item label="整体折扣率(10%-200%):" prop="discountRate">
    <el-input-number v-model="formData.discountRate" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
<el-form-item label="规则状态(1启用,0禁用):" prop="status">
  <el-select v-model.number="formData.status" placeholder="请选择状态">
    <el-option key="1" label="启用" value="1"></el-option>
    <el-option key="0" label="禁用" value="0"></el-option>
  </el-select>
</el-form-item>
          </el-form>
          <!-- 增加模拟计算 -->

    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="id字段">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="所属游戏ID">
    {{ detailFrom.gameId }}
</el-descriptions-item>
                    <el-descriptions-item label="规则状态(1启用,0禁用)">
    {{ detailFrom.status }}
</el-descriptions-item>
                    <el-descriptions-item label="指定角色代码列表，逗号分隔">
    {{ detailFrom.roleCodes }}
</el-descriptions-item>
                    <el-descriptions-item label="指定角色基础价">
    {{ detailFrom.roleBasePrice }}
</el-descriptions-item>
                    <el-descriptions-item label="指定服务器代码">
    {{ detailFrom.serverCode }}
</el-descriptions-item>
                    <el-descriptions-item label="服务器溢价百分比">
    {{ detailFrom.serverPremium }}
</el-descriptions-item>
                    <el-descriptions-item label="未指定角色定价倍率">
    {{ detailFrom.unspecifiedMultiplier }}
</el-descriptions-item>
                    <el-descriptions-item label="数量条件">
    {{ detailFrom.quantityCondition }}
</el-descriptions-item>
                    <el-descriptions-item label="整体折扣率(10%-200%)">
    {{ detailFrom.discountRate }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createPricingRule,
  deletePricingRule,
  deletePricingRuleByIds,
  updatePricingRule,
  findPricingRule,
  getPricingRuleList,
  calePricing
} from '@/api/Game/pricingRule'
// 导入获取游戏列表的API
import { getGameList } from '@/api/Game/game'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, watch } from 'vue'
import { useAppStore } from "@/pinia"

var price = ref(0)
var ruleNum = ref(0)
var logText = ref('')

defineOptions({
    name: 'PricingRule'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 游戏选项列表
const gameOptions = ref([])

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            gameId: undefined,
            roleCodes: '',
            roleBasePrice: 0,
            serverCode: '',
            serverPremium: 0,
            unspecifiedMultiplier: 0,
            quantityCondition: '',
            discountRate: 0,
            status: 1, // 默认为启用状态
        })

// 搜索信息对象
const searchInfo = ref({
  gameId: null, // 将游戏ID直接放在searchInfo中

})

// 获取游戏列表并设置默认游戏
const getGameOptions = async () => {
  try {
    const res = await getGameList({ page: 1, pageSize: 1000, isEnable: 1 }) // 获取启用的游戏
    if (res.code === 0 && res.data.list && res.data.list.length > 0) {
      gameOptions.value = res.data.list
      
      searchInfo.value.gameId = gameOptions.value[0].id
      
      // 获取表格数据
      getTableData()
    }
  } catch (error) {
    console.error('获取游戏列表失败:', error)
  }
}

// 处理游戏选择变化
const handleGameChange = (val) => {
  searchInfo.value.gameId = val
  // 更新到appStore
  appStore.setCurrentGameId(val)
  // 重新获取表格数据
  getTableData()
}

// 监听searchInfo.gameId变化，自动更新formData中的gameId
watch(() => searchInfo.value.gameId, (newVal) => {
  formData.value.gameId = newVal
})

// 在组件挂载时获取游戏列表
onMounted(() => {
  getGameOptions()
})

// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            roleBasePrice: 'role_base_price',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  const gameId = searchInfo.value.gameId
  searchInfo.value = { gameId }
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.status === ""){
        searchInfo.value.status=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getPricingRuleList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deletePricingRuleFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deletePricingRuleByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updatePricingRuleFunc = async(row) => {
    const res = await findPricingRule({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deletePricingRuleFunc = async (row) => {
    const res = await deletePricingRule({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    formData.value.gameId = searchInfo.value.gameId
    dialogFormVisible.value = true
}

//计算价格
const calculatePrice = async () => {
  const res = await calePricing({
    gameId: searchInfo.value.gameId,
    roleCodes:formData.value.roleCodes,
    roleBasePrice: formData.value.roleBasePrice,
    serverCode: formData.value.serverCode,
    unspecifiedMultiplier: formData.value.unspecifiedMultiplier,
    quantityCondition: formData.value.quantityCondition,
    discountRate: formData.value.discountRate,
  })
  if (res.code === 0) {
    price.value = res.data.price
    logText.value = res.data.logText
    ruleNum.value = res.data.ruleNum
    ElMessage({
      type: 'success',
      message: '计算成功'
    })
    formData.value.serverPremium = res.data.serverPremium
  }
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        gameId: searchInfo.value.gameId,
        roleCodes: '',
        roleBasePrice: 0,
        serverCode: '',
        serverPremium: 0,
        unspecifiedMultiplier: 0,
        quantityCondition: '',
        discountRate: 0,
        status: 1, // 默认为启用状态
    }
} // 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createPricingRule(formData.value)
                  break
                case 'update':
                  res = await updatePricingRule(formData.value)
                  break
                default:
                  res = await createPricingRule(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findPricingRule({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>







